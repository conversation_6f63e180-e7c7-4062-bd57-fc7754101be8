/**
 * Service for tracking and recording quiz statistics
 */

const database = require('../database/database');
const logger = require('../utils/logger');

/**
 * Record a completed quiz in the statistics
 * @param {Object} data - The quiz data
 * @param {string} data.userId - User ID
 * @param {string} data.username - Username
 * @param {string} data.quizType - Quiz type (MCQ/TF)
 * @param {string} data.topic - Quiz topic
 * @param {number} data.score - User's score
 * @param {number} data.totalQuestions - Total number of questions
 * @returns {Promise<number>} The ID of the inserted record
 */
async function recordQuizCompletion(data) {
  try {
    // Ensure required fields are present
    if (!data.userId || !data.quizType) {
      logger.error('Missing required fields for recordQuizCompletion');
      return null;
    }

    // Get database instance
    const db = database.db();
    
    if (!db) {
      logger.error('Database not initialized in recordQuizCompletion');
      return null;
    }

    // Insert quiz attempt into the database
    return new Promise((resolve, reject) => {
      db.run(
        `INSERT INTO quiz_attempts 
        (user_id, username, quiz_type, topic, score, total_questions, timestamp) 
        VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [
          data.userId,
          data.username || 'Unknown',
          data.quizType,
          data.topic || 'Unknown',
          data.score || 0,
          data.totalQuestions || 0,
          Date.now()
        ],
        function(err) {
          if (err) {
            logger.error(`Error recording quiz completion: ${err.message}`);
            resolve(null);
            return;
          }
          
          logger.info(`Recorded quiz completion for user ${data.userId}: ${data.score}/${data.totalQuestions}`);
          resolve(this.lastID);
        }
      );
    });
  } catch (error) {
    logger.error(`Error recording quiz completion: ${error.message}`);
    return null;
  }
}

/**
 * Get quiz statistics for all users
 * @param {number} limit - Maximum number of records to retrieve
 * @returns {Promise<Array>} Array of quiz statistics
 */
async function getQuizStats(limit = 100) {
  try {
    // Get database instance
    const db = database.db();
    
    if (!db) {
      logger.error('Database not initialized in getQuizStats');
      return [];
    }
    
    return new Promise((resolve, reject) => {
      db.all(
        `SELECT * FROM quiz_attempts 
         ORDER BY timestamp DESC 
         LIMIT ?`,
        [limit],
        (err, rows) => {
          if (err) {
            logger.error(`Error getting quiz stats: ${err.message}`);
            resolve([]);
            return;
          }
          
          resolve(rows || []);
        }
      );
    });
  } catch (error) {
    logger.error(`Error getting quiz stats: ${error.message}`);
    return [];
  }
}

/**
 * Get statistics for a specific user
 * @param {string} userId - The user ID
 * @param {number} limit - Maximum number of records to retrieve
 * @returns {Promise<Array>} Array of user's quiz statistics
 */
async function getUserQuizStats(userId, limit = 20) {
  try {
    if (!userId) {
      logger.error('Missing userId for getUserQuizStats');
      return [];
    }

    // Get database instance
    const db = database.db();
    
    if (!db) {
      logger.error('Database not initialized in getUserQuizStats');
      return [];
    }

    return new Promise((resolve, reject) => {
      db.all(
        `SELECT * FROM quiz_attempts 
         WHERE user_id = ? 
         ORDER BY timestamp DESC 
         LIMIT ?`,
        [userId, limit],
        (err, rows) => {
          if (err) {
            logger.error(`Error getting quiz stats for user ${userId}: ${err.message}`);
            resolve([]);
            return;
          }
          
          resolve(rows || []);
        }
      );
    });
  } catch (error) {
    logger.error(`Error getting quiz stats for user ${userId}: ${error.message}`);
    return [];
  }
}

/**
 * Get quiz summary statistics
 * @returns {Promise<Object>} Summary statistics
 */
async function getQuizSummaryStats() {
  try {
    // Get database instance
    const db = database.db();
    
    if (!db) {
      logger.error('Database not initialized in getQuizSummaryStats');
      return {
        totalQuizzes: 0,
        averageScore: 0,
        quizTypes: [],
        uniqueUsers: 0
      };
    }
    
    // Get total quizzes taken
    const totalQuizzes = await new Promise((resolve, reject) => {
      db.get(
        'SELECT COUNT(*) as count FROM quiz_attempts',
        (err, row) => {
          if (err) {
            logger.error(`Error getting total quizzes count: ${err.message}`);
            resolve({ count: 0 });
            return;
          }
          
          resolve(row || { count: 0 });
        }
      );
    });

    // Get average score
    const avgScore = await new Promise((resolve, reject) => {
      db.get(
        'SELECT AVG(score * 100.0 / total_questions) as average FROM quiz_attempts WHERE total_questions > 0',
        (err, row) => {
          if (err) {
            logger.error(`Error getting average score: ${err.message}`);
            resolve({ average: 0 });
            return;
          }
          
          resolve(row || { average: 0 });
        }
      );
    });

    // Get quiz type distribution
    const quizTypes = await new Promise((resolve, reject) => {
      db.all(
        'SELECT quiz_type, COUNT(*) as count FROM quiz_attempts GROUP BY quiz_type',
        (err, rows) => {
          if (err) {
            logger.error(`Error getting quiz type distribution: ${err.message}`);
            resolve([]);
            return;
          }
          
          resolve(rows || []);
        }
      );
    });

    // Get total unique users
    const uniqueUsers = await new Promise((resolve, reject) => {
      db.get(
        'SELECT COUNT(DISTINCT user_id) as count FROM quiz_attempts',
        (err, row) => {
          if (err) {
            logger.error(`Error getting unique users count: ${err.message}`);
            resolve({ count: 0 });
            return;
          }
          
          resolve(row || { count: 0 });
        }
      );
    });

    return {
      totalQuizzes: totalQuizzes?.count || 0,
      averageScore: avgScore?.average ? Math.round(avgScore.average) : 0,
      quizTypes: quizTypes || [],
      uniqueUsers: uniqueUsers?.count || 0
    };
  } catch (error) {
    logger.error(`Error getting quiz summary stats: ${error.message}`);
    return {
      totalQuizzes: 0,
      averageScore: 0,
      quizTypes: [],
      uniqueUsers: 0
    };
  }
}

/**
 * Record a model usage in the database
 * @param {string} model - The model identifier
 * @param {boolean} success - Whether the request was successful
 * @param {number} duration - Duration in milliseconds
 * @param {Object} [options] - Additional options
 * @param {string} [options.statusCode] - HTTP status code 
 * @param {string} [options.errorType] - Type of error if any
 */
function recordModelUsage(model, success, duration, options = {}) {
  try {
    if (!model) {
      logger.warn('No model specified for recordModelUsage');
      return;
    }
    
    // Format for storage
    const timestamp = Date.now();
    const formattedModel = model.replace(/[^\w\-\.\/]/g, '_'); // Sanitize model name
    
    // Store in model_stats table
    db.run(
      'INSERT INTO model_stats (model, success, duration, status_code, error_type, timestamp) VALUES (?, ?, ?, ?, ?, ?)',
      [
        formattedModel,
        success ? 1 : 0,
        duration || 0,
        options.statusCode || null,
        options.errorType || null,
        timestamp
      ],
      function(err) {
        if (err) {
          logger.error(`Error recording model usage stats: ${err.message}`);
        }
      }
    );
  } catch (error) {
    logger.error(`Error in recordModelUsage: ${error.message}`);
  }
}

/**
 * Get stats for model performance
 * @param {number} days - Number of days to look back
 * @returns {Promise<Object>} Object with model stats
 */
async function getModelStats(days = 7) {
  return new Promise((resolve, reject) => {
    const daysAgo = Date.now() - (days * 24 * 60 * 60 * 1000);
    
    // Query for getting model stats within time range
    const query = `
      SELECT 
        model,
        COUNT(*) as total_requests,
        SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful_requests,
        AVG(CASE WHEN success = 1 THEN duration ELSE NULL END) as avg_success_duration,
        MAX(timestamp) as last_used
      FROM model_stats
      WHERE timestamp >= ?
      GROUP BY model
      ORDER BY total_requests DESC
    `;
    
    db.all(query, [daysAgo], (err, rows) => {
      if (err) {
        logger.error(`Error getting model stats: ${err.message}`);
        reject(err);
        return;
      }
      
      // Format the results
      const stats = rows.map(row => ({
        model: row.model,
        totalRequests: row.total_requests,
        successRate: row.total_requests > 0 
          ? (row.successful_requests / row.total_requests) * 100 
          : 0,
        avgDuration: row.avg_success_duration || 0,
        lastUsed: new Date(row.last_used).toISOString()
      }));
      
      resolve(stats);
    });
  });
}

/**
 * Get cache usage statistics
 * @returns {Promise<Object>} Cache stats
 */
async function getCacheStats() {
  return new Promise((resolve, reject) => {
    // Query to get cache hits and misses from usage_stats
    const query = `
      SELECT 
        SUM(CASE WHEN stat_key = 'cache_hit' THEN value ELSE 0 END) as cache_hits,
        SUM(CASE WHEN stat_key = 'cache_miss' THEN value ELSE 0 END) as cache_misses
      FROM usage_stats
      WHERE stat_key IN ('cache_hit', 'cache_miss')
    `;
    
    db.all(query, [], (err, rows) => {
      if (err) {
        logger.error(`Error getting cache stats: ${err.message}`);
        reject(err);
        return;
      }
      
      const row = rows[0] || { cache_hits: 0, cache_misses: 0 };
      const total = row.cache_hits + row.cache_misses;
      
      resolve({
        hits: row.cache_hits || 0,
        misses: row.cache_misses || 0,
        total: total,
        hitRate: total > 0 ? (row.cache_hits / total) * 100 : 0
      });
    });
  });
}

/**
 * Get the total number of active users
 * @returns {Promise<number>} Number of active users
 */
async function getTotalUsers() {
  return new Promise((resolve, reject) => {
    db.get('SELECT COUNT(DISTINCT telegram_id) as count FROM users', [], (err, row) => {
      if (err) {
        logger.error(`Error getting user count: ${err.message}`);
        reject(err);
        return;
      }
      
      resolve(row?.count || 0);
    });
  });
}

/**
 * Get the number of active users in the last 24 hours
 * @returns {Promise<number>} Number of active users in last 24h
 */
async function getActiveUsersLast24h() {
  return new Promise((resolve, reject) => {
    const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);
    
    db.get('SELECT COUNT(DISTINCT telegram_id) as count FROM users WHERE last_activity >= ?', 
      [oneDayAgo], (err, row) => {
        if (err) {
          logger.error(`Error getting active user count: ${err.message}`);
          reject(err);
          return;
        }
        
        resolve(row?.count || 0);
      });
  });
}

/**
 * Get the total number of questions generated
 * @returns {Promise<number>} Total questions generated
 */
async function getTotalQuestionsGenerated() {
  return new Promise((resolve, reject) => {
    db.get('SELECT value FROM usage_stats WHERE stat_key = ?', ['questions_generated'], (err, row) => {
      if (err) {
        logger.error(`Error getting total questions: ${err.message}`);
        reject(err);
        return;
      }
      
      resolve(row?.value || 0);
    });
  });
}

module.exports = {
  recordQuizCompletion,
  getQuizStats,
  getUserQuizStats,
  getQuizSummaryStats,
  recordModelUsage,
  getModelStats,
  getCacheStats,
  getTotalUsers,
  getActiveUsersLast24h,
  getTotalQuestionsGenerated
}; 