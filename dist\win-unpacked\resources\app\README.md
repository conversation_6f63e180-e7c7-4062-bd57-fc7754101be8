# MCQ & True/False Question Generator Bot

A Telegram bot that generates multiple-choice and true/false questions from educational content. Helps students study and teachers create quizzes efficiently using AI.

## Features

- **Question Generation**
  - Generate multiple-choice questions (MCQ) from text content
  - Generate true/false questions with balanced distribution
  - Automatic explanations for each answer

- **Multiple Input Methods**
  - Direct text input
  - Image upload with OCR processing
  - PDF document upload (with text extraction and OCR for scanned PDFs)
  
- **Interactive Quiz System**
  - Take interactive tests with question-by-question navigation
  - Immediate feedback after each answer
  - Summary report with score and performance statistics
  
- **Intelligent Caching System**
  - Caches generated questions to reduce API usage
  - Similarity matching for related content
  
- **Model Selection & Testing**
  - Support for multiple AI models
  - Model testing capabilities for administrators
  - Performance tracking for different models
  
- **Admin Features**
  - Comprehensive statistics dashboard
  - User activity monitoring
  - Cache management
  - API key management
  - Model performance analysis

- **Required Channel Subscription**
  - Option to require users to subscribe to a Telegram channel
  - Membership verification with custom join buttons

## How It Works

### Question Generation Flow

1. User selects question type (MCQ or TF)
2. User provides content (text, image, or PDF)
3. <PERSON><PERSON> processes the content to extract text
4. <PERSON><PERSON> checks cache for similar content
5. If not found in cache, sends content to AI model
6. <PERSON><PERSON> formats questions and presents to user
7. User can take a quiz or view all questions with answers

### Interactive Quiz System

1. User clicks "Start Quiz" button
2. Bot presents questions one by one with multiple-choice options
3. User answers each question and gets immediate feedback
4. After final question, a summary with score is displayed
5. Quiz statistics are recorded in database

### Channel Subscription Requirement

1. Bot checks if user is a member of the required channel
2. Non-members are prompted to join with a direct link
3. "Check Membership" button verifies after joining

## Python Document Extraction Component

The bot uses a Python-based document extraction system for processing PDF, image, and DOCX files.

### Features

- Fast PDF text extraction using PyMuPDF
- OCR for images and scanned PDFs using Tesseract
- DOCX document processing
- Automatic question count adjustment:
  - For PDFs/DOCXs with more than 2 pages: 5 questions per page (max 50)
  - For PDFs/DOCXs with 1 page: 10 questions
  - For image files: 15 questions

### Requirements

Both Python and Tesseract OCR are required for the document extraction functionality:

#### 1. Install Python

Download and install Python 3.9+ from the official website:
https://www.python.org/downloads/

During installation:
- ✅ Check "Add Python to PATH" (ESSENTIAL)
- ✅ Select "Install for all users"

#### 2. Install Tesseract OCR

Download and install Tesseract OCR from:
https://github.com/UB-Mannheim/tesseract/wiki

The recommended installer is:
https://digi.bib.uni-mannheim.de/tesseract/tesseract-ocr-w64-setup-5.3.3.20231005.exe

During installation:
- Install to the default location (C:\Program Files\Tesseract-OCR)
- ✅ Make sure to select "Arabic" as an additional language

#### 3. Install Python Dependencies

Open a command prompt and run:

```
pip install -r python_requirements.txt
```

### Troubleshooting Document Extraction

1. **"Python is not recognized as a command"**:
   - Make sure Python is installed and added to PATH during installation
   - Try restarting your computer after installing Python

2. **"No module named 'pytesseract'"**:
   - Run `pip install -r python_requirements.txt` again
   - Try installing the package directly: `pip install pytesseract`

3. **Tesseract errors**:
   - Verify Tesseract is installed to the correct location
   - Check that the path in `simple_extraction_service.py` matches your installation
   - Make sure the Arabic language pack is installed

4. **Poor OCR quality**:
   - Try improving document image quality before uploading
   - Check that the Arabic language pack is installed for Tesseract

## Configuration

The bot uses environment variables:
```
# Bot Configuration
TELEGRAM_BOT_TOKEN=your_bot_token
ADMIN_IDS=admin_id1,admin_id2
REQUIRED_CHANNEL_ID=your_channel_id

# Question Generation Settings
DEFAULT_QUESTION_COUNT=15

# Cache Settings
CACHE_MAX_AGE_HOURS=72
CLEANUP_INTERVAL_HOURS=24

# Rate Limiting
REQUESTS_PER_DAY=20
RESET_INTERVAL_HOURS=24

# Models Configuration
MODELS=google/gemini-2.0-flash-exp:free,nvidia/llama-3.1-nemotron-70b-instruct:free,google/gemma-3-27b-it:free
REQUEST_TIMEOUT=60000

# API Key
API_KEY=your_openrouter_api_key

# Logging Configuration
DEBUG_MODE=false
LOG_QUESTION_DATA=false
```

## Core Components

1. **Bot Engine** - Built on Telegraf.js framework
2. **Database System** - SQLite database for data storage
3. **AI Integration** - Uses OpenRouter for model access
4. **Document Processing** - Python system with PyMuPDF and Tesseract OCR
5. **Session Management** - Persistent user sessions with debounced saving

## Admin Functions

- **Statistics Dashboard** - User metrics, question counts, and cache statistics
- **Model Status** - Performance metrics for each AI model
- **Cache Cleanup** - Manage cached questions
- **Model Testing** - Compare AI models for quality and performance

## Getting Started

1. Clone the repository
2. Install dependencies: 
   - `npm install` (Node.js dependencies)
   - `pip install -r python_requirements.txt` (Python dependencies)
3. Install Tesseract OCR with Arabic language support
4. Configure environment variables in `.env`
5. Start the bot: `node src/bot.js`

## File Structure

```
├── src/
│   ├── bot.js                    # Main bot file
│   ├── config.js                 # Configuration manager
│   ├── database/                 # Database operations
│   │   └── database.js           # SQLite implementation
│   ├── handlers/                 # Message handlers
│   │   ├── adminHandlers.js      # Admin command handlers
│   │   ├── commandHandlers.js    # Command handlers
│   │   ├── menuHandlers.js       # Menu handlers
│   │   └── messageHandlers.js    # Content handlers
│   ├── services/                 # Core services
│   │   ├── apiService.js         # AI API integration
│   │   ├── feedbackService.js    # User feedback
│   │   ├── fileService.js        # File processing
│   │   ├── modelTester.js        # AI model testing
│   │   ├── sessionService.js     # Session management
│   │   └── statsService.js       # Statistics tracking
│   └── utils/                    # Utility functions
│       ├── escapeHtml.js         # HTML escaping
│       ├── helpers.js            # Helper functions
│       ├── logger.js             # Logging system
│       ├── questionUtils.js      # Question utilities
│       └── userUtils.js          # User management
├── simple_extraction_service_fixed.py  # Python extraction script
├── python_extract.js             # JS-Python bridge
└── data/                         # Data storage
    ├── bot.db                    # SQLite database
    ├── sessions.json             # User sessions
    └── users.json                # User tracking
```

## Commands

- `/start` - Start the bot
- `/help` - Display help information
- `/admin` - Access admin panel (admin only)
- `/stats` - View usage statistics (admin only)
- `/keys` - Manage API keys (admin only)
- `/users` - View user statistics (admin only)
- `/modelstatus` - View model performance (admin only)
- `/cleanupCache` - Clear cache (admin only)
- `/testmodels` - Test AI models (admin only)

## Example Use Cases

1. **Students studying for exams**
   - Upload textbook pages to generate practice questions
   - Take quizzes to test knowledge
   - Review explanations for incorrect answers

2. **Teachers creating assessments**
   - Generate question sets from lecture notes or textbooks
   - Edit and select the best questions for classroom use
   - Track student performance through quiz statistics

3. **Self-learners**
   - Test comprehension of new material
   - Practice recall with generated questions
   - Identify knowledge gaps through performance analysis

# MCQ_TF Bot Code Review

After reviewing the Telegram MCQ/TF Question Generator code, here are potential issues and recommendations:

## Code Issues Found

### Security Issues

1. **Hardcoded Paths**: The code contains hardcoded paths that may not exist on a target system:
   ```javascript
   pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
   ```

2. **API Key Management**: API keys are stored as environment variables but also in a SQLite database without encryption.

3. **Insufficient Input Validation**: When processing user-provided input, validation could be stricter.

### System Architecture Issues

1. **Error Handling**: Some error handling is inconsistent across the codebase.
   - Some errors silently pass through with only logs
   - Others might crash the application

2. **Memory Management**: Large documents could cause memory issues since there's no clear limit on document content stored in memory.

3. **Database Connection Management**: Database connection is initialized but not explicitly closed in some paths.

### Performance Issues

1. **Cache Management**: Cache is stored both in memory and database, which could lead to consistency issues.

2. **Model Selection Logic**: The model selection logic is complex and could be simplified:
   ```javascript
   // In apiService.js
   function selectModel() {
     // Complex logic with multiple nested conditions
   }
   ```

3. **Resource Cleanup**: Some temporary files might not be properly cleaned up if processes crash.

## Recommendations

### Immediate Fixes

1. **Path Configuration**: Move all hardcoded paths to configuration variables:
   ```javascript
   // Change this:
   pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
   
   // To this:
   pytesseract.pytesseract.tesseract_cmd = config.tesseractPath || r'C:\Program Files\Tesseract-OCR\tesseract.exe'
   ```

2. **Add Graceful Shutdown**: Ensure all resources (database connections, file handles) are properly closed on shutdown:
   ```javascript
   process.on('SIGINT', () => {
     // Close database connections
     // Clean temp files
     // etc.
     process.exit(0);
   });
   ```

3. **Better Error Boundaries**: Add better error handling to prevent crashes and enhance user experience.

### Architecture Improvements

1. **Service Monitoring**: Add health checks and monitoring endpoints to track system status.

2. **Rate Limiting**: Improve the rate limiting logic to be more consistent and configurable.

3. **File Processing Pipeline**: Refactor file processing to be more modular and testable.

4. **Database Migrations**: Add proper database migration system for future updates.

## Testing Recommendations

1. Create unit tests for critical functions like model selection and question parsing.

2. Add integration tests for complete document processing flow.

3. Implement stress testing for high load scenarios.

## Deployment Recommendations

1. Use Docker for consistent environment configuration.

2. Implement proper logging with rotation.

3. Consider separating CPU-intensive operations (OCR) from the main bot server.
