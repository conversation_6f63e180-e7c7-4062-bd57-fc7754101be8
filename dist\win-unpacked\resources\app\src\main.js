const { app, BrowserWindow, ipcMain, dialog, Menu, shell } = require('electron');
const path = require('path');
const fs = require('fs');

// Load environment variables
require('dotenv').config();

// Import existing services (adapted for desktop)
const database = require('./database/database');
const config = require('./config/desktop'); // Use desktop-specific config
const logger = require('./utils/logger');
const IPCHandlers = require('./ipcHandlers');
const packagedEnvironment = require('./config/packaged-environment');

// Create necessary directories using packaged environment
const dataDir = packagedEnvironment.isDevelopment()
  ? path.join(__dirname, '../data')
  : packagedEnvironment.getUserDataPath();

if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
  logger.info('Created data directory');
}

// Initialize packaged environment paths
if (!packagedEnvironment.isDevelopment()) {
  logger.info('Running in packaged mode');
  logger.info(`User data path: ${packagedEnvironment.getUserDataPath()}`);
  logger.info(`Database path: ${packagedEnvironment.getDatabasePath()}`);
}

let mainWindow;
let splashWindow;

function createSplashWindow() {
  splashWindow = new BrowserWindow({
    width: 400,
    height: 300,
    frame: false,
    alwaysOnTop: true,
    transparent: true,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true
    }
  });

  splashWindow.loadFile(path.join(__dirname, 'renderer/splash.html'));
  
  splashWindow.on('closed', () => {
    splashWindow = null;
  });
}

function createMainWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    show: false,
    icon: path.join(__dirname, '../assets/icon.png'),
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    }
  });

  // Maximize the window when it's ready to show
  mainWindow.maximize();

  // Load the main HTML file
  mainWindow.loadFile(path.join(__dirname, 'renderer/index.html'));

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    if (splashWindow) {
      splashWindow.close();
    }
    mainWindow.show();
    
    // Focus on window
    if (process.platform === 'darwin') {
      app.dock.show();
    }
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Open external links in default browser
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });

  // Development tools
  if (process.env.NODE_ENV === 'development') {
    mainWindow.webContents.openDevTools();
  }
}

// Create application menu
function createMenu() {
  const template = [
    {
      label: 'File',
      submenu: [
        {
          label: 'New Quiz',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            mainWindow.webContents.send('menu-new-quiz');
          }
        },
        {
          label: 'Open File',
          accelerator: 'CmdOrCtrl+O',
          click: async () => {
            const result = await dialog.showOpenDialog(mainWindow, {
              properties: ['openFile'],
              filters: [
                { name: 'Documents', extensions: ['pdf', 'docx', 'doc', 'txt'] },
                { name: 'Images', extensions: ['jpg', 'jpeg', 'png', 'bmp', 'tiff'] },
                { name: 'All Files', extensions: ['*'] }
              ]
            });
            
            if (!result.canceled && result.filePaths.length > 0) {
              mainWindow.webContents.send('file-selected', result.filePaths[0]);
            }
          }
        },

        { type: 'separator' },
        {
          label: 'Exit',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'Quiz',
      submenu: [
        {
          label: 'Generate MCQ',
          accelerator: 'CmdOrCtrl+M',
          click: () => {
            mainWindow.webContents.send('menu-generate-mcq');
          }
        },
        {
          label: 'Generate True/False',
          accelerator: 'CmdOrCtrl+T',
          click: () => {
            mainWindow.webContents.send('menu-generate-tf');
          }
        },
        { type: 'separator' },
        {
          label: 'Start Interactive Quiz',
          accelerator: 'CmdOrCtrl+I',
          click: () => {
            mainWindow.webContents.send('menu-start-quiz');
          }
        }
      ]
    },
    {
      label: 'View',
      submenu: [
        {
          label: 'Statistics',
          click: () => {
            mainWindow.webContents.send('menu-statistics');
          }
        },
        {
          label: 'History',
          click: () => {
            mainWindow.webContents.send('menu-history');
          }
        },
        { type: 'separator' },
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' }
      ]
    },
    {
      label: 'Help',
      submenu: [
        {
          label: 'About',
          click: () => {
            mainWindow.webContents.send('menu-about');
          }
        },
        {
          label: 'User Guide',
          click: () => {
            mainWindow.webContents.send('menu-help');
          }
        }
      ]
    }
  ];

  // macOS specific menu adjustments
  if (process.platform === 'darwin') {
    template.unshift({
      label: app.getName(),
      submenu: [
        { role: 'about' },
        { type: 'separator' },
        { role: 'services' },
        { type: 'separator' },
        { role: 'hide' },
        { role: 'hideOthers' },
        { role: 'unhide' },
        { type: 'separator' },
        { role: 'quit' }
      ]
    });
  }

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// App event handlers
app.whenReady().then(async () => {
  try {
    // Initialize database
    logger.info('Initializing database...');
    database.initDatabase();

    // Initialize IPC handlers
    const ipcHandlers = new IPCHandlers();
    ipcHandlers.initializeDesktopTables();

    // Create splash screen
    createSplashWindow();

    // Create main window after a short delay
    setTimeout(() => {
      createMainWindow();
      createMenu();

      // Set the main window reference in IPC handlers after window is created
      ipcHandlers.setMainWindow(mainWindow);
    }, 2000);

    logger.success('Application started successfully!');
  } catch (error) {
    logger.error(`Failed to start application: ${error.message}`);
    app.quit();
  }
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createMainWindow();
  }
});

// Prevent navigation to external URLs
app.on('web-contents-created', (event, contents) => {
  contents.on('will-navigate', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl);
    
    if (parsedUrl.origin !== 'file://') {
      event.preventDefault();
    }
  });
});

module.exports = { mainWindow };
