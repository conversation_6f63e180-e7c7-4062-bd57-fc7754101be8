// Configuration for the desktop application
// This file provides configuration variables for the desktop app

// Export configuration variables
module.exports = {
  token: process.env.TELEGRAM_BOT_TOKEN || process.env.TELEGRAM_TOKEN,
  apiKey: process.env.API_KEY || process.env.API_KEYS?.split(',')[0],
  adminIds: (process.env.ADMIN_IDS || process.env.ADMIN_USER_IDS || '').split(',').map(id => id.trim()),
  cacheMaxAgeHours: parseInt(process.env.CACHE_MAX_AGE_HOURS || '72', 10),
  cleanupIntervalHours: parseInt(process.env.CLEANUP_INTERVAL_HOURS || '24', 10),
  requestsPerDay: parseInt(process.env.REQUESTS_PER_DAY || '20', 10),
  resetIntervalHours: parseInt(process.env.RESET_INTERVAL_HOURS || '24', 10),
  defaultQuestionCount: parseInt(process.env.DEFAULT_QUESTION_COUNT || '15', 10),
  models: (process.env.MODELS || '').split(',').filter(Boolean),
  requestTimeout: parseInt(process.env.REQUEST_TIMEOUT || '60000', 10),
  debugMode: process.env.DEBUG_MODE === 'true',
  logQuestionData: process.env.LOG_QUESTION_DATA === 'true',
  requiredChannelId: process.env.REQUIRED_CHANNEL_ID || null
}; 