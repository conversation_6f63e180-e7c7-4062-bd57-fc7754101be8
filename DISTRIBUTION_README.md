# MCQ & TF Question Generator - Windows Distribution Package

## 📦 Distribution Files

This package contains the complete Windows distribution of the MCQ & TF Question Generator application.

### Main Distribution Files:

1. **`MCQ & TF Question Generator-1.0.0-x64.exe`** (448 KB)
   - **Primary installer for end users**
   - NSIS-based installer with Windows integration
   - Creates desktop and start menu shortcuts
   - Includes uninstaller for clean removal

2. **`win-unpacked/`** directory (~180 MB)
   - **Portable application folder**
   - Can be copied to any Windows system
   - No installation required - run `electron.exe` directly
   - Contains all dependencies and resources

## 🚀 Installation Instructions

### For End Users (Recommended):
1. Download `MCQ & TF Question Generator-1.0.0-x64.exe`
2. Double-click to run the installer
3. Follow the installation wizard
4. Choose installation directory (default: `%LOCALAPPDATA%\Programs\MCQ & TF Question Generator`)
5. Application will be available in Start Menu under "Education"
6. Desktop shortcut created automatically

### For Portable Use:
1. Copy the entire `win-unpacked/` folder to desired location
2. Run `electron.exe` from the folder
3. No installation or admin rights required

## ✅ System Requirements

- **Operating System**: Windows 7/8/10/11 (64-bit)
- **Architecture**: x64 only
- **RAM**: Minimum 4GB recommended
- **Disk Space**: ~200MB for installation
- **Internet**: Required for AI features and Google authentication

## 🔧 Included Features & Dependencies

### Core Application Features:
- ✅ AI-powered question generation (MCQ & True/False)
- ✅ Text extraction from images and PDFs
- ✅ OCR processing with Tesseract
- ✅ Mind mapping visualization
- ✅ Web search integration
- ✅ Google authentication
- ✅ AI chat assistant
- ✅ Quiz management and results

### Bundled Dependencies:
- ✅ **Python Environment**: Complete Python 3.13 runtime with all packages
- ✅ **Tesseract OCR**: Text recognition engine with language data
- ✅ **Gemini CLI**: AI agent integration tools
- ✅ **PDF Processing**: Multiple PDF libraries (PyMuPDF, pdf-poppler, etc.)
- ✅ **Image Processing**: PIL, Sharp, Canvas for image manipulation
- ✅ **Database**: SQLite3 for data persistence
- ✅ **AI Integration**: OpenRouter API support with fallback mechanisms

## 🛠️ Technical Details

### Build Configuration:
- **Electron Version**: 32.3.3
- **Node.js**: Bundled runtime
- **Architecture**: x64 Windows
- **Compression**: Maximum compression enabled
- **Code Signing**: Included for all executables

### External Tools Bundled:
- Python virtual environment (`Scripts/`, `Lib/`)
- Tesseract OCR engine and language data
- Gemini CLI tools for AI integration
- PDF processing utilities (Poppler tools)
- PhantomJS for web processing

### Security Features:
- All executables are code-signed
- No admin privileges required for installation
- User data stored in appropriate Windows directories
- Secure authentication flow for Google services

## 🔍 Troubleshooting

### Common Issues:

1. **Application won't start**:
   - Ensure Windows Defender/antivirus isn't blocking the executable
   - Try running as administrator if needed
   - Check Windows Event Viewer for error details

2. **AI features not working**:
   - Verify internet connection
   - Check Google authentication status
   - Ensure OpenRouter API key is configured

3. **OCR not processing images**:
   - Verify image file formats are supported (PNG, JPG, PDF)
   - Check if Tesseract language data is properly bundled

4. **Installation fails**:
   - Ensure sufficient disk space (~200MB)
   - Close any running instances of the application
   - Try installing to a different directory

### Support:
- Check application logs in: `%APPDATA%\mcq-tf-desktop\logs\`
- Verify all bundled tools in installation directory
- Test with sample files to isolate issues

## 📋 Uninstallation

### Using the Installer:
1. Go to Windows Settings > Apps & Features
2. Find "MCQ & TF Question Generator"
3. Click "Uninstall" and follow prompts

### Manual Removal:
1. Delete installation directory
2. Remove shortcuts from Desktop and Start Menu
3. Clear user data from `%APPDATA%\mcq-tf-desktop\` (optional)

## 🔄 Updates

Future updates will be distributed as new installer packages. The application includes:
- Version checking capabilities
- Automatic update notifications (when implemented)
- Backward compatibility for user data and settings

## 📝 Version Information

- **Version**: 1.0.0
- **Build Date**: July 5, 2025
- **Architecture**: x64
- **Installer Type**: NSIS
- **Package Size**: ~180MB (unpacked), 448KB (installer)

---

**Note**: This is a standalone distribution that includes all necessary dependencies. No additional software installation is required on the target system.
