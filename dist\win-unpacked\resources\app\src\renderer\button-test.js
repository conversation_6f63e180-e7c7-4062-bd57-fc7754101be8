// Button functionality test utility
class ButtonTester {
    constructor() {
        this.testResults = [];
        this.criticalButtons = [
            'quizGeneratorService',
            'textToolsService', 
            'pdfEditorService',
            'mcqBtn',
            'tfBtn',
            'textInputBtn',
            'fileUploadBtn',
            'imageUploadBtn',
            'generateFromText',
            'startQuizBtn',
            'showAnswersBtn',
            'exportQuestionsBtn',
            'submitAnswer',
            'nextQuestion',
            'finishQuiz',
            'floatingBackBtn',
            'headerBackBtn',
            'historyBtn',
            'statsBtn'
        ];
    }

    async runButtonTests() {
        console.log('🧪 Starting comprehensive button functionality tests...');
        this.testResults = [];

        // Test 1: Check if all critical buttons exist
        this.testButtonExistence();

        // Test 2: Check if buttons have proper event listeners
        this.testEventListeners();

        // Test 3: Test button state management
        this.testButtonStates();

        // Test 4: Test navigation flow
        await this.testNavigationFlow();

        // Test 5: Test quiz button sequence
        await this.testQuizButtonSequence();

        this.reportResults();
    }

    testButtonExistence() {
        console.log('🔍 Testing button existence...');
        
        this.criticalButtons.forEach(buttonId => {
            const button = document.getElementById(buttonId);
            const exists = !!button;
            
            this.testResults.push({
                test: 'Button Existence',
                buttonId,
                passed: exists,
                message: exists ? 'Button found' : 'Button missing'
            });
        });
    }

    testEventListeners() {
        console.log('🔗 Testing event listeners...');
        
        this.criticalButtons.forEach(buttonId => {
            const button = document.getElementById(buttonId);
            if (button) {
                // Check if button has click event listeners
                const hasListeners = button.onclick !== null || 
                                   button.addEventListener !== undefined;
                
                this.testResults.push({
                    test: 'Event Listeners',
                    buttonId,
                    passed: hasListeners,
                    message: hasListeners ? 'Has event handling' : 'No event listeners detected'
                });
            }
        });
    }

    testButtonStates() {
        console.log('🎛️ Testing button states...');
        
        // Test disabled state management
        const submitBtn = document.getElementById('submitAnswer');
        if (submitBtn) {
            const initiallyDisabled = submitBtn.disabled;
            this.testResults.push({
                test: 'Button States',
                buttonId: 'submitAnswer',
                passed: initiallyDisabled,
                message: initiallyDisabled ? 'Correctly disabled initially' : 'Should be disabled initially'
            });
        }

        // Test button visibility
        const nextBtn = document.getElementById('nextQuestion');
        const finishBtn = document.getElementById('finishQuiz');
        
        if (nextBtn && finishBtn) {
            const nextHidden = nextBtn.classList.contains('hidden');
            const finishHidden = finishBtn.classList.contains('hidden');
            
            this.testResults.push({
                test: 'Button States',
                buttonId: 'nextQuestion',
                passed: nextHidden,
                message: nextHidden ? 'Correctly hidden initially' : 'Should be hidden initially'
            });
            
            this.testResults.push({
                test: 'Button States', 
                buttonId: 'finishQuiz',
                passed: finishHidden,
                message: finishHidden ? 'Correctly hidden initially' : 'Should be hidden initially'
            });
        }
    }

    async testNavigationFlow() {
        console.log('🧭 Testing navigation flow...');
        
        // Test home screen navigation
        const homeBtn = document.getElementById('floatingBackBtn');
        if (homeBtn) {
            try {
                // Simulate click without actually triggering
                const clickEvent = new Event('click', { bubbles: true });
                const canDispatch = homeBtn.dispatchEvent !== undefined;
                
                this.testResults.push({
                    test: 'Navigation Flow',
                    buttonId: 'floatingBackBtn',
                    passed: canDispatch,
                    message: canDispatch ? 'Can handle navigation events' : 'Cannot handle navigation events'
                });
            } catch (error) {
                this.testResults.push({
                    test: 'Navigation Flow',
                    buttonId: 'floatingBackBtn',
                    passed: false,
                    message: `Navigation error: ${error.message}`
                });
            }
        }
    }

    async testQuizButtonSequence() {
        console.log('🎯 Testing quiz button sequence...');
        
        const submitBtn = document.getElementById('submitAnswer');
        const nextBtn = document.getElementById('nextQuestion');
        const finishBtn = document.getElementById('finishQuiz');
        
        if (submitBtn && nextBtn && finishBtn) {
            // Test initial state
            const initialState = {
                submitDisabled: submitBtn.disabled,
                nextHidden: nextBtn.classList.contains('hidden'),
                finishHidden: finishBtn.classList.contains('hidden')
            };
            
            const correctInitialState = initialState.submitDisabled && 
                                      initialState.nextHidden && 
                                      initialState.finishHidden;
            
            this.testResults.push({
                test: 'Quiz Button Sequence',
                buttonId: 'quiz-buttons',
                passed: correctInitialState,
                message: correctInitialState ? 'Correct initial quiz button state' : 'Incorrect initial quiz button state'
            });
        }
    }

    reportResults() {
        console.log('📊 Button Test Results:');
        console.log('========================');
        
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(result => result.passed).length;
        const failedTests = totalTests - passedTests;
        
        console.log(`Total Tests: ${totalTests}`);
        console.log(`Passed: ${passedTests}`);
        console.log(`Failed: ${failedTests}`);
        console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
        
        console.log('\nDetailed Results:');
        this.testResults.forEach(result => {
            const status = result.passed ? '✅' : '❌';
            console.log(`${status} ${result.test} - ${result.buttonId}: ${result.message}`);
        });
        
        if (failedTests > 0) {
            console.log('\n⚠️ Issues found! Check the failed tests above.');
        } else {
            console.log('\n🎉 All button tests passed!');
        }
        
        return {
            total: totalTests,
            passed: passedTests,
            failed: failedTests,
            successRate: (passedTests / totalTests) * 100,
            results: this.testResults
        };
    }
}

// Export for use in console
window.ButtonTester = ButtonTester;

// Auto-run tests when loaded (can be disabled)
if (window.location.search.includes('test=buttons')) {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
            const tester = new ButtonTester();
            tester.runButtonTests();
        }, 2000); // Wait for app to initialize
    });
}
