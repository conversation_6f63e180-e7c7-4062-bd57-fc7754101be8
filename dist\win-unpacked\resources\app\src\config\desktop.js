// Desktop-specific configuration (removes Telegram dependencies)
require('dotenv').config();

const fs = require('fs');
const path = require('path');
const envPath = path.resolve(__dirname, '../../.env');

// Function to load environment variables from file if not already set
function loadEnvFromFile() {
  try {
    if (fs.existsSync(envPath)) {
      const envContent = fs.readFileSync(envPath, 'utf8');
      // Process each line
      envContent.split('\n').forEach(line => {
        // Skip comments and empty lines
        if (line.trim() && !line.startsWith('#')) {
          const equalSignPos = line.indexOf('=');
          if (equalSignPos !== -1) {
            const key = line.substring(0, equalSignPos).trim();
            const value = line.substring(equalSignPos + 1).trim();
            
            // Only set if not already in process.env
            if (!process.env[key]) {
              process.env[key] = value;
            }
          }
        }
      });
      console.log('Loaded environment variables from file directly');
    } else {
      console.log('.env file not found at:', envPath);
    }
  } catch (error) {
    console.error('Error loading environment variables from file:', error);
  }
}

// Load environment variables from file if needed
loadEnvFromFile();

// Get environment variables
const questionsPerPage = parseInt(process.env.QUESTIONS_PER_PAGE || '5', 10);
const imageQuestionsCount = parseInt(process.env.IMAGE_QUESTIONS_COUNT || '5', 10);
const maxConcurrentRequests = parseInt(process.env.MAX_CONCURRENT_REQUESTS || '8', 10);
const maxRequestsPerModel = parseInt(process.env.MAX_REQUESTS_PER_MODEL || '2', 10);
const maxRequestsPerUser = parseInt(process.env.MAX_REQUESTS_PER_USER || '2', 10);
const cacheTTL = parseInt(process.env.CACHE_TTL || '86400', 10); // 24 hours in seconds
const maxCacheSize = parseInt(process.env.MAX_CACHE_SIZE || '500', 10); // Maximum cache entries
const apiTimeout = parseInt(process.env.API_TIMEOUT || '180000', 10); // 3 minutes timeout for API calls

// Load models from custom configuration or use defaults
function loadModels() {
  const customModelsPath = path.join(__dirname, 'models.json');

  // Try to load custom models first
  if (fs.existsSync(customModelsPath)) {
    try {
      const customModels = JSON.parse(fs.readFileSync(customModelsPath, 'utf8'));
      if (customModels.length > 0) {
        console.log(`Loaded ${customModels.length} custom models from models.json`);
        return customModels.map(model => model.id);
      }
    } catch (error) {
      console.warn('Error loading custom models:', error.message);
    }
  }

  // No fallback models - user must configure their own models
  console.log('No custom models found - user must add models through the UI');
  return [];
}

const models = loadModels();

// Configure supported document types
const supportedFileTypes = [
  'application/pdf',
  'text/plain',
  'image/jpeg',
  'image/png', 
  'image/jpg',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // docx
  'application/msword', // doc
  'application/vnd.openxmlformats-officedocument.presentationml.presentation', // pptx
  'application/vnd.ms-powerpoint', // ppt
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // xlsx
  'application/vnd.ms-excel', // xls
  'text/rtf', // RTF
  'text/html', // HTML
  'application/vnd.oasis.opendocument.text', // ODT
  'application/vnd.oasis.opendocument.presentation' // ODP
];

// Desktop-specific configuration (no Telegram dependencies)
const config = {
  // Application settings
  appName: 'MCQ & TF Question Generator',
  appVersion: '1.0.0',
  
  // Question generation settings
  questionsPerPage,
  imageQuestionsCount,
  defaultQuestionCount: parseInt(process.env.DEFAULT_QUESTION_COUNT || '15'),
  
  // File handling
  fileUploadsPerDay: parseInt(process.env.FILE_UPLOADS_PER_DAY || '50'), // Increased for desktop
  maxFileSizeMB: parseInt(process.env.MAX_FILE_SIZE_MB || '1000'), // 1GB max
  maxImageSize: 1000 * 1024 * 1024, // 1000MB in bytes
  maxDocumentSize: 1000 * 1024 * 1024, // 1000MB in bytes
  supportedFileTypes,
  
  // Cache settings
  cacheMaxAgeHours: parseInt(process.env.CACHE_MAX_AGE_HOURS || '72'),
  cleanupIntervalHours: parseInt(process.env.CLEANUP_INTERVAL_HOURS || '24'),
  cacheTTL,
  maxCacheSize,
  
  // API settings
  defaultApiUrl: 'https://openrouter.ai/api/v1/chat/completions',
  models,
  requestTimeout: parseInt(process.env.REQUEST_TIMEOUT || '60000'),
  apiKey: process.env.API_KEY || '',
  apiKeys: [process.env.API_KEY || ''].filter(key => key.trim() !== ''),
  apiTimeout,
  
  // Concurrency management
  maxConcurrentRequests,
  maxRequestsPerModel,
  maxRequestsPerUser,
  
  // Performance optimization
  extractionCacheTTL: 3600000, // 1 hour in milliseconds
  extractionCacheSize: 50,
  extractionParallelWorkers: 4,
  extractionTimeoutMs: 600000, // 10 minutes
  
  // Desktop-specific settings
  autoSave: true,
  autoBackup: true,
  backupInterval: 24 * 60 * 60 * 1000, // 24 hours in milliseconds
  maxBackups: 10,
  
  // UI settings
  theme: 'light',
  language: 'en',
  notifications: true,
  soundEffects: false,
  
  // Database settings (use existing bot.db for now)
  databasePath: path.join(__dirname, '../../data/bot.db'),
  backupPath: path.join(__dirname, '../../data/backups'),
  
  // Logging
  debug: process.env.DEBUG_MODE === 'true',
  logLevel: process.env.LOG_LEVEL || 'info',
  logToFile: true,
  logPath: path.join(__dirname, '../../logs'),
  
  // Security (for future features)
  encryptData: false,
  requirePassword: false,
  sessionTimeout: 30 * 60 * 1000, // 30 minutes
  
  // Export settings
  exportFormats: ['json', 'csv', 'txt', 'pdf'],
  defaultExportFormat: 'json',
  
  // Quiz settings
  defaultQuizMode: 'interactive',
  showExplanations: true,
  shuffleQuestions: true,  // Enable smart shuffling to prevent predictable patterns
  shuffleOptions: false,
  timeLimit: null, // No time limit by default
  
  // Statistics
  trackStatistics: true,
  anonymizeData: true
};

console.log('Desktop Configuration Loaded:');
console.log('- App Name:', config.appName);
console.log('- Version:', config.appVersion);
console.log('- API Key available:', config.apiKey ? 'Yes' : 'No');
console.log('- Models available:', config.models.length);
console.log('- Database path:', config.databasePath);
console.log('- Debug mode:', config.debug);

module.exports = config;
