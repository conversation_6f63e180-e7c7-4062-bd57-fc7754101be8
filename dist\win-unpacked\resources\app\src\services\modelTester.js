const axios = require('axios');
const fs = require('fs');
const path = require('path');
const config = require('../config');

// Sample large text for testing
const SAMPLE_TEXT = `
Artificial Intelligence (AI) refers to the simulation of human intelligence in machines that are programmed to think and learn like humans. The term may also be applied to any machine that exhibits traits associated with a human mind such as learning and problem-solving. The ideal characteristic of artificial intelligence is its ability to rationalize and take actions that have the best chance of achieving a specific goal.

Machine Learning (ML) is a subset of artificial intelligence that provides systems the ability to automatically learn and improve from experience without being explicitly programmed. Machine learning focuses on the development of computer programs that can access data and use it to learn for themselves. The process of learning begins with observations or data, such as examples, direct experience, or instruction, in order to look for patterns in data and make better decisions in the future based on the examples that we provide. The primary aim is to allow the computers to learn automatically without human intervention or assistance and adjust actions accordingly.

Deep Learning is a subset of machine learning that has networks capable of learning unsupervised from data that is unstructured or unlabeled. Also known as deep neural learning or deep neural network, it is a technique that teaches computers to do what comes naturally to humans: learn by example. Deep learning is a key technology behind driverless cars, enabling them to recognize a stop sign, or to distinguish a pedestrian from a lamppost. It is the key to voice control in consumer devices like phones, tablets, TVs, and hands-free speakers.

Natural Language Processing (NLP) is a field of artificial intelligence that gives computers the ability to understand text and spoken words in much the same way as human beings can. NLP combines computational linguistics—rule-based modeling of human language—with statistical, machine learning, and deep learning models. Together, these technologies enable computers to process human language in the form of text or voice data and to 'understand' its full meaning, complete with the speaker or writer's intent and sentiment.

Computer Vision is a field of artificial intelligence that trains computers to interpret and understand the visual world. Using digital images from cameras and videos and deep learning models, machines can accurately identify and classify objects — and then react to what they "see." Computer vision is used in industries ranging from energy and utilities to manufacturing and automotive – and the market is continuing to grow.

Reinforcement Learning is an area of machine learning concerned with how software agents ought to take actions in an environment in order to maximize the notion of cumulative reward. Reinforcement learning is one of three basic machine learning paradigms, alongside supervised learning and unsupervised learning. Reinforcement learning differs from supervised learning in that labeled input/output pairs need not be presented, and sub-optimal actions need not be explicitly corrected.

Generative AI refers to artificial intelligence systems that can generate novel content such as text, images, audio, and synthetic data. These systems learn from existing content to produce new outputs that maintain similarities to the training data while also incorporating novel elements. Key technologies within generative AI include generative adversarial networks (GANs), variational autoencoders (VAEs), large language models (LLMs), diffusion models, and transformers. Recent examples of generative AI systems include GPT (Generative Pre-trained Transformer) for text generation, DALL-E for image creation from text descriptions, and various music and voice generation systems. Generative AI has wide-ranging applications across creative industries, data augmentation, simulation, and content creation, but also raises significant concerns regarding copyright, misinformation, and ethics.
`;

// Test configuration
const TEST_CONFIG = {
  mcqCount: 10,
  tfCount: 10,
  timeout: 90000 // 90 seconds timeout
};

/**
 * Test a specific model for MCQ/TF question generation
 * @param {string} model Model identifier
 * @param {string} apiKey API key
 * @returns {Promise<Object>} Test results
 */
async function testModel(model, apiKey) {
  console.log(`Testing model: ${model}`);
  
  const results = {
    model,
    mcq: {
      success: false,
      duration: 0,
      questionCount: 0,
      error: null
    },
    tf: {
      success: false,
      duration: 0,
      questionCount: 0,
      error: null
    }
  };
  
  // Test MCQ generation
  try {
    console.log(`Testing ${model} for MCQ generation...`);
    const mcqStart = Date.now();
    
    const mcqResult = await generateQuestions(model, apiKey, 'MCQ', TEST_CONFIG.mcqCount);
    
    results.mcq.duration = Date.now() - mcqStart;
    results.mcq.success = !!mcqResult;
    results.mcq.questionCount = mcqResult ? mcqResult.length : 0;
  } catch (error) {
    console.error(`Error testing ${model} for MCQ:`, error.message);
    results.mcq.error = error.message;
  }
  
  // Test TF generation
  try {
    console.log(`Testing ${model} for TF generation...`);
    const tfStart = Date.now();
    
    const tfResult = await generateQuestions(model, apiKey, 'TF', TEST_CONFIG.tfCount);
    
    results.tf.duration = Date.now() - tfStart;
    results.tf.success = !!tfResult;
    results.tf.questionCount = tfResult ? tfResult.length : 0;
  } catch (error) {
    console.error(`Error testing ${model} for TF:`, error.message);
    results.tf.error = error.message;
  }
  
  return results;
}

/**
 * Generate questions using a specific model
 * @param {string} model Model identifier
 * @param {string} apiKey API key
 * @param {string} type Question type (MCQ or TF)
 * @param {number} count Number of questions to generate
 * @returns {Promise<Array|null>} Generated questions or null on error
 */
async function generateQuestions(model, apiKey, type, count) {
  // Prepare the prompt based on question type
  let prompt;
  if (type === 'MCQ') {
    prompt = `Please generate ${count} multiple choice questions (MCQs) based on the following text. Each question should have one correct answer and three incorrect answers. 
    
    Text: ${SAMPLE_TEXT}
    
    Format each question as a JSON object with these properties:
    - question: The full text of the question
    - options: An array of 4 answer choices (A, B, C, D)
    - correctAnswer: The letter of the correct answer (A, B, C, or D)
    - explanation: A brief explanation of why the correct answer is right
    
    Return your response as a JSON array of question objects. Ensure all questions relate directly to the provided text.
    
    Example format:
    [
      {
        "question": "What is the capital of France?",
        "options": ["A. London", "B. Paris", "C. Berlin", "D. Rome"],
        "correctAnswer": "B",
        "explanation": "Paris is the capital city of France."
      }
    ]`;
  } else {
    prompt = `Please generate ${count} true/false questions based on the following text:
    
    Text: ${SAMPLE_TEXT}
    
    Format each question as a JSON object with these properties:
    - question: The full text of the statement to evaluate as true or false
    - isTrue: A boolean value (true or false) indicating if the statement is true
    - explanation: A brief explanation of why the statement is true or false
    
    Return your response as a JSON array of question objects. Ensure all questions relate directly to the provided text.
    
    Example format:
    [
      {
        "question": "Paris is the capital of France.",
        "isTrue": true,
        "explanation": "Paris is indeed the capital city of France."
      }
    ]
    
    Important: Try to create a balanced set with approximately equal numbers of true and false statements.`;
  }
  
  // Make the API request
  try {
    const response = await axios.post('https://openrouter.ai/api/v1/chat/completions', {
      model: model,
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.7,
      max_tokens: 2048
    }, {
      headers: {
        'Authorization': `Bearer ${apiKey}`, 
        'Content-Type': 'application/json', 
        'HTTP-Referer': 'https://telegram-mcq-tf-bot.com', 
        'X-Title': 'Telegram MCQ/TF Question Generator'
      },
      timeout: TEST_CONFIG.timeout
    });
    
    // Process response
    if (response.data && response.data.choices && response.data.choices.length > 0) {
      const content = response.data.choices[0].message.content;
      
      // Try to extract JSON from the response
      try {
        // Find JSON array in content (it might be surrounded by markup or explanation)
        const jsonMatch = content.match(/\[[\s\S]*\]/);
        if (jsonMatch) {
          // Clean the JSON string by removing trailing commas which can cause parse errors
          const cleanedJson = jsonMatch[0].replace(/,(\s*[\]}])/g, '$1').replace(/,(\s*\])/g, '$1');
          const questions = JSON.parse(cleanedJson);
          return questions;
        }
      } catch (error) {
        console.error('Error parsing questions JSON:', error);
        throw new Error('Failed to parse response JSON');
      }
    }
    
    throw new Error('Invalid API response format');
  } catch (error) {
    console.error(`API request error for ${model}:`, error.message);
    throw error;
  }
}

/**
 * Test multiple models and return ranked results
 * @param {Array<string>} models List of models to test
 * @param {string} apiKey API key
 * @returns {Promise<Array>} Ranked test results
 */
async function testModels(models, apiKey) {
  const results = [];
  
  for (const model of models) {
    try {
      const result = await testModel(model, apiKey);
      results.push(result);
      
      // Add a small delay between tests to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 2000));
    } catch (error) {
      console.error(`Failed to test model ${model}:`, error);
      results.push({
        model,
        mcq: { success: false, error: error.message },
        tf: { success: false, error: error.message }
      });
    }
  }
  
  // Rank results
  return rankModels(results);
}

/**
 * Rank models based on test results
 * @param {Array} results Test results
 * @returns {Array} Ranked results
 */
function rankModels(results) {
  // Calculate a score for each model
  const scoredResults = results.map(result => {
    const mcqScore = result.mcq.success ? 
      (10 * result.mcq.questionCount - result.mcq.duration / 1000) : -1000;
    
    const tfScore = result.tf.success ? 
      (10 * result.tf.questionCount - result.tf.duration / 1000) : -1000;
    
    return {
      ...result,
      totalScore: mcqScore + tfScore,
      mcqScore,
      tfScore
    };
  });
  
  // Sort by total score (descending)
  return scoredResults.sort((a, b) => b.totalScore - a.totalScore);
}

/**
 * Run a full test and save results to file
 * @param {string} apiKey API key
 */
async function runFullTest(apiKey) {
  // Define models to test
  const modelsToTest = [
    'nvidia/llama-3.1-nemotron-70b-instruct:free',
    'qwen/qwen2.5-vl-32b-instruct:free',
    'google/gemini-2.0-flash-thinking-exp:free',
    'google/gemma-3-27b-it:free',
    'deepseek/deepseek-r1:free',
    'google/gemini-2.0-flash-exp:free',
    'deepseek/deepseek-chat-v3-0324:free'
  ];
  
  console.log(`Starting model test with ${modelsToTest.length} models`);
  
  try {
    const rankedResults = await testModels(modelsToTest, apiKey);
    
    // Save results to file
    const resultsPath = path.resolve(__dirname, '../../data/model-test-results.json');
    fs.writeFileSync(resultsPath, JSON.stringify({
      timestamp: new Date().toISOString(),
      results: rankedResults
    }, null, 2), 'utf8');
    
    console.log('Model testing completed. Results saved to data/model-test-results.json');
    
    // Log top 3 models
    console.log('\nTop 3 recommended models:');
    rankedResults.slice(0, 3).forEach((result, index) => {
      console.log(`${index + 1}. ${result.model} (Score: ${result.totalScore.toFixed(2)})`);
      console.log(`   MCQ: ${result.mcq.success ? `Success - ${result.mcq.questionCount} questions in ${(result.mcq.duration/1000).toFixed(2)}s` : 'Failed'}`);
      console.log(`   TF: ${result.tf.success ? `Success - ${result.tf.questionCount} questions in ${(result.tf.duration/1000).toFixed(2)}s` : 'Failed'}`);
    });
    
    return rankedResults;
  } catch (error) {
    console.error('Error running full model test:', error);
    throw error;
  }
}

module.exports = {
  testModel,
  testModels,
  runFullTest
}; 