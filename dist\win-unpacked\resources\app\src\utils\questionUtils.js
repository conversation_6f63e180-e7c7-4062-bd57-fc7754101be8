// Utility functions for managing questions
const crypto = require('crypto');

/**
 * Map type codes to full question type names
 * @param {string} typeCode - The type code (MCQ or TF)
 * @returns {string} Full type name
 */
function getFullTypeName(typeCode) {
  return typeCode === 'MCQ' ? 'Multiple‑Choice' : 'True/False';
}

/**
 * Calculate similarity between two strings
 * @param {string} str1 First string
 * @param {string} str2 Second string
 * @returns {number} Similarity score 0-1 where 1 is identical
 */
function calculateSimilarity(str1, str2) {
  if (!str1 || !str2) return 0;
  if (str1 === str2) return 1.0;
  
  const str1Words = str1.split(/\s+/).filter(Boolean);
  const str2Words = str2.split(/\s+/).filter(Boolean);
  
  if (str1Words.length === 0 || str2Words.length === 0) return 0;
  
  // Create sets of words
  const set1 = new Set(str1Words);
  const set2 = new Set(str2Words);
  
  // Count words in common
  let intersection = 0;
  for (const word of set1) {
    if (set2.has(word)) {
      intersection++;
    }
  }
  
  // Jaccard similarity
  const union = set1.size + set2.size - intersection;
  return union ? intersection / union : 0;
}

/**
 * Simplified similarity check comparing two texts
 * @param {string} text1 First text
 * @param {string} text2 Second text
 * @param {number} threshold Minimum similarity threshold (0-1)
 * @returns {boolean} Whether texts are similar enough
 */
function areSimilarTexts(text1, text2, threshold = 0.7) {
  if (!text1 || !text2) return false;
  if (text1 === text2) return true;
  
  // Process and filter texts
  const filtered1 = filterTextForCaching(text1);
  const filtered2 = filterTextForCaching(text2);
  
  // Calculate similarity
  const similarityScore = calculateSimilarity(filtered1, filtered2);
  
  // Return true if similar enough
  return similarityScore >= threshold;
}

/**
 * Filter text to prepare for caching/comparison
 * @param {string} text - The text to filter
 * @returns {string} Filtered text
 */
function filterTextForCaching(text) {
  if (!text) return '';
  
  // Remove common non-content words
  let filtered = text
    .toLowerCase()
    // Remove special characters and digits
    .replace(/[^\w\s]/g, ' ')
    .replace(/\d+/g, ' ')
    // Remove extra spaces
    .replace(/\s+/g, ' ')
    .trim();
  
  return filtered;
}

/**
 * Generate a hash for caching purposes
 * @param {string} text - Text content to hash
 * @param {string} type - Question type (MCQ or TF)
 * @returns {string} - Hash string
 */
function generateHash(text, type) {
  // Normalize the type
  const normalizedType = getFullTypeName(type).toLowerCase();
  
  // Take first 1000 chars and last 1000 chars for long texts
  const textToHash = text.length > 2000 
    ? text.substring(0, 1000) + text.substring(Math.max(0, text.length - 1000))
    : text;
  
  // Create a hash
  return crypto
    .createHash('md5')
    .update(`${normalizedType}:${textToHash}`)
    .digest('hex');
}

/**
 * Add explicit answers to questions if missing
 * @param {string} questionsText - Raw generated questions
 * @param {string} type - Question type
 * @returns {string} Enhanced questions
 */
function addExplicitAnswers(questionsText, type) {
  const lines = questionsText.split('\n');
  const enhancedLines = [];
  
  let currentQuestionIndex = -1;
  let currentOptions = [];
  let foundAnswerForCurrentQuestion = false;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    if (!line) {
      enhancedLines.push('');
      continue;
    }
    
    enhancedLines.push(line);
    
    // Look for question number at start of line (e.g. "1. Question text")
    const questionMatch = line.match(/^(\d+)[\.\)]\s+(.+)/);
    if (questionMatch) {
      if (currentQuestionIndex >= 0 && !foundAnswerForCurrentQuestion) {
        // Previous question had no answer, try to determine one
        const probableAnswer = determineProbableAnswer(lines, currentQuestionIndex, i, currentOptions, type);
        enhancedLines.push(`Answer: ${probableAnswer}`);
      }
      
      currentQuestionIndex = i;
      currentOptions = [];
      foundAnswerForCurrentQuestion = false;
      continue;
    }
    
    // Look for answer options
    const optionMatch = line.match(/^([A-D])(?:[\s:\.\)\-]+)(.+)/i);
    if (optionMatch && currentQuestionIndex >= 0) {
      currentOptions.push({
        label: optionMatch[1].toUpperCase(),
        index: i
      });
      continue;
    }
    
    // Look for answer lines
    const answerMatch = line.match(/^(?:Answer|Correct|الاجابة|الإجابة)[:s]*([A-D])/i);
    if (answerMatch && currentQuestionIndex >= 0) {
      foundAnswerForCurrentQuestion = true;
      continue;
    }
  }
  
  // Check if the last question had an answer
  if (currentQuestionIndex >= 0 && !foundAnswerForCurrentQuestion) {
    // Last question had no answer, try to determine one
    const probableAnswer = determineProbableAnswer(lines, currentQuestionIndex, lines.length, currentOptions, type);
    enhancedLines.push(`Answer: ${probableAnswer}`);
  }
  
  return enhancedLines.join('\n');
}

/**
 * Determine a probable answer based on question content
 * @param {Array<string>} lines - Question text lines
 * @param {number} startIndex - Start index
 * @param {number} endIndex - End index
 * @param {Array<Object>} options - Answer options
 * @param {string} type - Question type
 * @returns {string} Probable answer
 */
function determineProbableAnswer(lines, startIndex, endIndex, options, type) {
  if (type.toLowerCase() === 'tf' || type.toLowerCase() === 'true/false') {
    // For true/false questions, use a more balanced approach
    
    // Keep track of global true/false distribution to maintain balance
    if (!global.tfAnswerCount) {
      global.tfAnswerCount = { true: 0, false: 0 };
    }
    
    const questionText = lines[startIndex].toLowerCase();
    
    // Calculate current ratio to force more false answers when imbalanced
    const ratio = global.tfAnswerCount.true / (global.tfAnswerCount.false || 1);
    
    // Strong indicators for False answer
    const falseIndicators = questionText.includes('not ') || 
                           questionText.includes('false') || 
                           questionText.includes('incorrect') || 
                           questionText.includes('never') ||
                           questionText.includes('cannot');
    
    // Apply balance-based decision making
    let answer;
    if (ratio > 1.5) {
      // If we have too many True answers, bias toward False
      answer = falseIndicators || Math.random() < 0.7 ? 'B' : 'A';
    } else if (ratio < 0.67) {
      // If we have too many False answers, bias toward True
      answer = falseIndicators && Math.random() < 0.3 ? 'B' : 'A';
    } else {
      // If balance is ok, use indicators but with some randomness
      answer = falseIndicators && Math.random() < 0.7 ? 'B' : 'A';
    }
    
    // Update our global counter
    if (answer === 'A') {
      global.tfAnswerCount.true++;
    } else {
      global.tfAnswerCount.false++;
    }
    
    console.log(`Determined probable answer ${answer} for TF question. Current balance: ${global.tfAnswerCount.true} True, ${global.tfAnswerCount.false} False`);
    return answer;
  } else {
    // For MCQs, use a simple heuristic based on option length - longer options are often correct
    let longestOption = null;
    let longestLength = 0;
    
    if (!options || options.length === 0) {
      return 'A'; // Default if no options found
    }
    
    for (const option of options) {
      const optionText = lines[option.index].replace(/^[A-D](?:[\s:\.\)\-]+)/, '').trim();
      if (optionText.length > longestLength) {
        longestLength = optionText.length;
        longestOption = option.label;
      }
    }
    
    // If we couldn't determine, pick a random option or default to A
    return longestOption || options[Math.floor(Math.random() * options.length)]?.label || 'A';
  }
}

/**
 * Clean explanation text
 * @param {string} text - Text to clean
 * @returns {string} - Cleaned text
 */
function cleanExplanationText(text) {
  if (!text) return '';
  
  // Remove any JSON formatting artifacts
  return text
    .replace(/^["']/, '')  // Remove leading quotes
    .replace(/["']$/, '')  // Remove trailing quotes
    .replace(/\\n/g, '\n') // Convert escaped newlines
    .replace(/\\"/g, '"')  // Convert escaped quotes
    .replace(/\\\\/g, '\\'); // Convert escaped backslashes
}

/**
 * Module exports
 */
module.exports = {
  getFullTypeName,
  addExplicitAnswers,
  determineProbableAnswer,
  generateHash,
  filterTextForCaching,
  cleanExplanationText
}; 