const GeminiService = require('./geminiService');

class AIChatService {
    constructor(database = null) {
        this.geminiService = new GeminiService();
        this.studentId = 'desktop-user'; // Default student ID for desktop app
        this.chatHistory = [];
        this.isInitialized = false;
        this.currentLanguage = 'en';
        this.database = database; // Database instance passed from main process
    }

    /**
     * Initialize the AI chat service
     */
    async initialize() {
        try {
            // Check if <PERSON> is authenticated
            const authStatus = await this.geminiService.checkAuthStatus();
            this.isInitialized = authStatus === 'authenticated';

            if (this.isInitialized) {
                // Ensure chat history table exists
                await this.ensureChatHistoryTable();

                // Load chat history if available
                await this.loadChatHistory();

                // Initialize student profile
                await this.initializeStudentProfile();
            }

            return this.isInitialized;
        } catch (error) {
            console.error('Error initializing AI chat service:', error);
            this.isInitialized = false;
            return false;
        }
    }

    /**
     * Ensure chat history table exists
     */
    async ensureChatHistoryTable() {
        try {
            if (!this.database) return;

            const db = this.database.db();
            if (!db) return;

            return new Promise((resolve, reject) => {
                db.run(`
                    CREATE TABLE IF NOT EXISTS chat_history (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        student_id TEXT NOT NULL,
                        message_type TEXT NOT NULL,
                        content TEXT NOT NULL,
                        timestamp INTEGER NOT NULL,
                        language TEXT DEFAULT 'en',
                        is_automatic INTEGER DEFAULT 0
                    )
                `, (err) => {
                    if (err) {
                        console.error('Error creating chat_history table:', err);
                        reject(err);
                    } else {
                        console.log('Chat history table ensured');
                        resolve();
                    }
                });
            });
        } catch (error) {
            console.error('Error ensuring chat history table:', error);
        }
    }

    /**
     * Initialize student profile with basic information
     */
    async initializeStudentProfile() {
        try {
            const profileData = `Student profile initialized for desktop application. Learning preferences: interactive quizzes, visual content, personalized feedback.`;
            await this.geminiService.saveStudentMemory(this.studentId, profileData);
        } catch (error) {
            console.error('Error initializing student profile:', error);
        }
    }

    /**
     * Send a message to the AI and get response (optimized for speed)
     */
    async sendMessage(message, language = 'en') {
        try {
            if (!this.isInitialized) {
                return {
                    success: false,
                    error: 'AI chat service not initialized. Please sign in to AI Agent first.'
                };
            }

            this.currentLanguage = language;

            // Add user message to history
            const userMessage = {
                type: 'user',
                content: message,
                timestamp: Date.now(),
                language: language
            };
            this.chatHistory.push(userMessage);

            // Check for instant responses first (much faster)
            const instantResponse = this.getInstantResponse(message, language);
            if (instantResponse) {
                const actionButtons = await this.generateActionButtons(instantResponse, language);

                const aiMessage = {
                    type: 'ai',
                    content: instantResponse,
                    timestamp: Date.now(),
                    language: language,
                    actionButtons: actionButtons
                };
                this.chatHistory.push(aiMessage);
                await this.saveChatHistory();

                return {
                    success: true,
                    response: instantResponse,
                    actionButtons: actionButtons,
                    timestamp: aiMessage.timestamp,
                    source: 'instant'
                };
            }

            // For complex queries, use AI with timeout
            const aiPromise = this.getAIResponse(message, language);
            const timeoutPromise = new Promise((_, reject) =>
                setTimeout(() => reject(new Error('Response timeout')), 8000) // 8 second timeout
            );

            try {
                const response = await Promise.race([aiPromise, timeoutPromise]);

                if (response.success) {
                    const actionButtons = await this.generateActionButtons(response.response, language);

                    const aiMessage = {
                        type: 'ai',
                        content: response.response,
                        timestamp: Date.now(),
                        language: language,
                        actionButtons: actionButtons
                    };
                    this.chatHistory.push(aiMessage);
                    await this.saveChatHistory();

                    return {
                        success: true,
                        response: response.response,
                        actionButtons: actionButtons,
                        timestamp: aiMessage.timestamp,
                        source: 'ai'
                    };
                }
            } catch (timeoutError) {
                console.log('AI response timeout, using fallback');
                // Use fallback response
                const fallbackResponse = this.getFallbackResponse(message, language);
                const actionButtons = await this.generateActionButtons(fallbackResponse, language);

                const aiMessage = {
                    type: 'ai',
                    content: fallbackResponse,
                    timestamp: Date.now(),
                    language: language,
                    actionButtons: actionButtons
                };
                this.chatHistory.push(aiMessage);
                await this.saveChatHistory();

                return {
                    success: true,
                    response: fallbackResponse,
                    actionButtons: actionButtons,
                    timestamp: aiMessage.timestamp,
                    source: 'fallback'
                };
            }

            // Final fallback
            return this.getErrorResponse(language);

        } catch (error) {
            console.error('Error sending message to AI:', error);
            return this.getErrorResponse(language);
        }
    }

    /**
     * Record quiz completion and update student memory
     */
    async recordQuizCompletion(quizData) {
        try {
            if (!this.isInitialized) return { success: false };

            await this.geminiService.recordQuizCompletion(this.studentId, quizData);
            
            // Generate automatic encouragement message
            const percentage = Math.round((quizData.score / quizData.totalQuestions) * 100);
            let encouragementMessage = '';
            
            if (percentage >= 90) {
                encouragementMessage = this.currentLanguage === 'ar' ? 
                    `ممتاز! لقد حققت ${percentage}% في اختبار ${quizData.topic}. أداء رائع!` :
                    `Excellent! You scored ${percentage}% on the ${quizData.topic} quiz. Great job!`;
            } else if (percentage >= 70) {
                encouragementMessage = this.currentLanguage === 'ar' ? 
                    `جيد جداً! لقد حققت ${percentage}% في اختبار ${quizData.topic}. استمر في التحسن!` :
                    `Good work! You scored ${percentage}% on the ${quizData.topic} quiz. Keep improving!`;
            } else {
                encouragementMessage = this.currentLanguage === 'ar' ? 
                    `لقد حققت ${percentage}% في اختبار ${quizData.topic}. لا تقلق، يمكنك المحاولة مرة أخرى وستتحسن!` :
                    `You scored ${percentage}% on the ${quizData.topic} quiz. Don't worry, you can try again and improve!`;
            }

            // Add encouragement to chat history
            const encouragementMsg = {
                type: 'ai',
                content: encouragementMessage,
                timestamp: Date.now(),
                language: this.currentLanguage,
                isAutomatic: true
            };
            this.chatHistory.push(encouragementMsg);
            await this.saveChatHistory();

            return { success: true, encouragement: encouragementMessage };
        } catch (error) {
            console.error('Error recording quiz completion:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Record file processing activity
     */
    async recordFileActivity(fileData) {
        try {
            if (!this.isInitialized) return { success: false };

            await this.geminiService.recordFileActivity(this.studentId, fileData);
            return { success: true };
        } catch (error) {
            console.error('Error recording file activity:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Get study recommendations based on topic and processed files
     */
    async getStudyRecommendations(topic = '') {
        try {
            if (!this.isInitialized) {
                return {
                    success: false,
                    error: 'AI chat service not initialized'
                };
            }

            // Get recent file activity to understand what content the student has been studying
            const recentFiles = await this.getRecentFileActivity();
            const fileContext = recentFiles.length > 0 ?
                `Recent study materials: ${recentFiles.map(f => f.file_name || 'document').join(', ')}` :
                'No recent file activity';

            const prompt = this.currentLanguage === 'ar' ?
                `قدم نصائح دراسية مخصصة للموضوع: ${topic}.
                اعتمد على تاريخ أدائي وأنماط التعلم الخاصة بي.
                المواد الدراسية الحديثة: ${fileContext}
                قدم نصائح محددة بناءً على المحتوى الذي درسته مؤخراً.` :
                `Provide personalized study recommendations for the topic: ${topic}.
                Base your advice on my performance history and learning patterns.
                ${fileContext}
                Give specific advice based on the content I've been studying recently.`;

            const response = await this.geminiService.chatWithAI(prompt, this.studentId, this.currentLanguage);
            return response;
        } catch (error) {
            console.error('Error getting study recommendations:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Get recent file activity for context
     */
    async getRecentFileActivity() {
        try {
            if (!this.database) return [];

            const db = this.database.db();
            if (!db) return [];

            return new Promise((resolve) => {
                // Try file_activity table first (from simulation/enhanced tracking)
                db.all(`
                    SELECT file_name, file_type, timestamp
                    FROM file_activity
                    WHERE user_id = ?
                    ORDER BY timestamp DESC
                    LIMIT 5
                `, ['desktop-user'], (err, rows) => {
                    if (err || !rows || rows.length === 0) {
                        // Fallback to file_uploads table
                        db.all(`
                            SELECT user_id, count, reset_at as timestamp
                            FROM file_uploads
                            ORDER BY reset_at DESC
                            LIMIT 5
                        `, [], (err2, rows2) => {
                            resolve(rows2 || []);
                        });
                    } else {
                        resolve(rows);
                    }
                });
            });
        } catch (error) {
            console.error('Error getting recent file activity:', error);
            return [];
        }
    }

    /**
     * Get fast progress summary with optional detailed analysis
     */
    async getProgressSummary() {
        try {
            if (!this.isInitialized) {
                return {
                    success: false,
                    error: 'AI chat service not initialized'
                };
            }

            // Get user data quickly
            const userData = await this.gatherUserData();

            // Generate fast summary first
            const quickSummary = this.generateQuickSummary(userData, this.currentLanguage);

            // Return quick summary immediately
            return {
                success: true,
                summary: quickSummary,
                rawData: userData,
                source: 'quick'
            };

        } catch (error) {
            console.error('Error getting progress summary:', error);
            return {
                success: true,
                summary: this.currentLanguage === 'ar' ?
                    'أنت تحرز تقدماً جيداً! استمر في الممارسة المنتظمة.' :
                    'You\'re making good progress! Keep practicing regularly.',
                source: 'fallback'
            };
        }
    }

    /**
     * Generate quick summary without AI delay
     */
    generateQuickSummary(userData, language) {
        const performance = userData.performance || {};
        const patterns = userData.studyPatterns || {};

        if (language === 'ar') {
            return `📊 ملخص سريع:
• الاختبارات: ${performance.totalQuizzes || 0} اختبار
• المتوسط: ${performance.averageScore || 0}%
• الاتجاه: ${this.translateTrend(performance.improvement, 'ar')}
• نقاط القوة: ${performance.strongAreas?.join(', ') || 'قيد التحليل'}
• للتحسين: ${performance.weakAreas?.join(', ') || 'قيد التحليل'}
• تكرار الدراسة: ${patterns.studyFrequency || 'منتظم'}

${this.getQuickAdvice(performance, 'ar')}`;
        } else {
            return `📊 Quick Summary:
• Quizzes: ${performance.totalQuizzes || 0} completed
• Average: ${performance.averageScore || 0}%
• Trend: ${performance.improvement || 'Stable'}
• Strengths: ${performance.strongAreas?.join(', ') || 'Analyzing...'}
• Improve: ${performance.weakAreas?.join(', ') || 'Analyzing...'}
• Study frequency: ${patterns.studyFrequency || 'Regular'}

${this.getQuickAdvice(performance, 'en')}`;
        }
    }

    /**
     * Get quick advice based on performance
     */
    getQuickAdvice(performance, language) {
        const avgScore = parseFloat(performance.averageScore) || 0;

        if (language === 'ar') {
            if (avgScore >= 80) return '🎉 أداء ممتاز! استمر على هذا المنوال.';
            if (avgScore >= 70) return '👍 أداء جيد! ركز على المراجعة.';
            if (avgScore >= 60) return '📚 تحسن مستمر! زد وقت الدراسة.';
            return '💪 ابدأ بجلسات قصيرة ومراجعة منتظمة.';
        } else {
            if (avgScore >= 80) return '🎉 Excellent performance! Keep it up.';
            if (avgScore >= 70) return '👍 Good work! Focus on review.';
            if (avgScore >= 60) return '📚 Steady improvement! Increase study time.';
            return '💪 Start with short sessions and regular review.';
        }
    }

    /**
     * Translate trend to Arabic
     */
    translateTrend(trend, language) {
        if (language !== 'ar') return trend;

        switch (trend) {
            case 'Improving': return 'متحسن';
            case 'Declining': return 'يحتاج تحسين';
            case 'Stable': return 'مستقر';
            default: return 'قيد التحليل';
        }
    }

    /**
     * Gather comprehensive user data from all app activities
     */
    async gatherUserData() {
        try {
            const userData = {
                quizHistory: [],
                fileActivity: [],
                studyPatterns: {},
                performance: {},
                preferences: {},
                timeAnalysis: {}
            };

            if (!this.database) return userData;
            const db = this.database.db();
            if (!db) return userData;

            // Get quiz sessions data
            userData.quizHistory = await new Promise((resolve) => {
                db.all(`
                    SELECT * FROM quiz_sessions
                    ORDER BY created_at DESC
                    LIMIT 50
                `, [], (err, rows) => {
                    if (err) {
                        console.error('Error getting quiz history:', err);
                        resolve([]);
                    } else {
                        resolve(rows || []);
                    }
                });
            });

            // Get saved quizzes data
            userData.savedQuizzes = await new Promise((resolve) => {
                db.all(`
                    SELECT * FROM saved_quizzes
                    ORDER BY created_at DESC
                `, [], (err, rows) => {
                    if (err) {
                        console.error('Error getting saved quizzes:', err);
                        resolve([]);
                    } else {
                        resolve(rows || []);
                    }
                });
            });

            // Get file activity data (try both file_activity and file_uploads tables)
            userData.fileActivity = await new Promise((resolve) => {
                // First try the file_activity table (created by simulation)
                db.all(`
                    SELECT * FROM file_activity
                    ORDER BY timestamp DESC
                    LIMIT 30
                `, [], (err, rows) => {
                    if (err) {
                        // If file_activity doesn't exist, try file_uploads
                        db.all(`
                            SELECT user_id, count as file_count, reset_at as timestamp
                            FROM file_uploads
                            ORDER BY reset_at DESC
                            LIMIT 30
                        `, [], (err2, rows2) => {
                            if (err2) {
                                console.error('Error getting file activity:', err2);
                                resolve([]);
                            } else {
                                resolve(rows2 || []);
                            }
                        });
                    } else {
                        resolve(rows || []);
                    }
                });
            });

            // Analyze patterns
            userData.performance = this.analyzePerformance(userData.quizHistory);
            userData.studyPatterns = this.analyzeStudyPatterns(userData.quizHistory, userData.fileActivity);
            userData.timeAnalysis = this.analyzeTimePatterns(userData.quizHistory);

            return userData;
        } catch (error) {
            console.error('Error gathering user data:', error);
            return {};
        }
    }

    /**
     * Get saved quizzes and recommend specific ones
     */
    async getQuizRecommendations() {
        try {
            // Get saved quizzes from database
            const savedQuizzes = await this.getSavedQuizzesFromDB();

            if (!savedQuizzes || savedQuizzes.length === 0) {
                return {
                    success: true,
                    message: this.currentLanguage === 'ar' ?
                        'لا توجد اختبارات محفوظة حالياً.' :
                        'No saved quizzes available currently.'
                };
            }

            // Get AI recommendations based on quiz history
            const prompt = this.currentLanguage === 'ar' ?
                `بناءً على أدائي السابق، أي من هذه الاختبارات المحفوظة تنصحني بإعادة أخذها؟ ${JSON.stringify(savedQuizzes.map(q => ({ name: q.name, topic: q.topic })))}` :
                `Based on my past performance, which of these saved quizzes would you recommend I retake? ${JSON.stringify(savedQuizzes.map(q => ({ name: q.name, topic: q.topic })))}`;

            const response = await this.geminiService.chatWithAI(prompt, this.studentId, this.currentLanguage);

            return {
                success: true,
                recommendations: response.response,
                availableQuizzes: savedQuizzes
            };
        } catch (error) {
            console.error('Error getting quiz recommendations:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Load chat history from storage
     */
    async loadChatHistory() {
        try {
            if (!this.database) {
                this.chatHistory = [];
                return;
            }

            const db = this.database.db();
            if (!db) {
                this.chatHistory = [];
                return;
            }

            return new Promise((resolve, reject) => {
                db.all(
                    'SELECT * FROM chat_history WHERE student_id = ? ORDER BY timestamp ASC',
                    [this.studentId],
                    (err, rows) => {
                        if (err) {
                            console.error('Error loading chat history:', err);
                            this.chatHistory = [];
                            resolve();
                        } else {
                            this.chatHistory = rows.map(row => ({
                                type: row.message_type,
                                content: row.content,
                                timestamp: row.timestamp,
                                language: row.language,
                                isAutomatic: row.is_automatic === 1
                            }));
                            resolve();
                        }
                    }
                );
            });
        } catch (error) {
            console.error('Error loading chat history:', error);
            this.chatHistory = [];
        }
    }

    /**
     * Save chat history to storage
     */
    async saveChatHistory() {
        try {
            if (!this.database) return;

            const db = this.database.db();
            if (!db) return;

            // Clear existing history for this student
            await new Promise((resolve, reject) => {
                db.run('DELETE FROM chat_history WHERE student_id = ?', [this.studentId], (err) => {
                    if (err) reject(err);
                    else resolve();
                });
            });

            // Insert new history
            const stmt = db.prepare(`
                INSERT INTO chat_history (student_id, message_type, content, timestamp, language, is_automatic)
                VALUES (?, ?, ?, ?, ?, ?)
            `);

            for (const message of this.chatHistory) {
                stmt.run([
                    this.studentId,
                    message.type,
                    message.content,
                    message.timestamp,
                    message.language || 'en',
                    message.isAutomatic ? 1 : 0
                ]);
            }

            stmt.finalize();
        } catch (error) {
            console.error('Error saving chat history:', error);
        }
    }

    /**
     * Get chat history
     */
    getChatHistory() {
        return this.chatHistory;
    }

    /**
     * Clear chat history
     */
    async clearChatHistory() {
        try {
            this.chatHistory = [];

            if (!this.database) return { success: true };

            const db = this.database.db();
            if (!db) return { success: true };

            return new Promise((resolve, reject) => {
                db.run('DELETE FROM chat_history WHERE student_id = ?', [this.studentId], (err) => {
                    if (err) {
                        console.error('Error clearing chat history:', err);
                        resolve({ success: false, error: err.message });
                    } else {
                        resolve({ success: true });
                    }
                });
            });
        } catch (error) {
            console.error('Error clearing chat history:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Get saved quizzes from database
     */
    async getSavedQuizzesFromDB() {
        try {
            if (!this.database) return [];

            const db = this.database.db();
            if (!db) return [];

            return new Promise((resolve, reject) => {
                db.all('SELECT * FROM saved_quizzes ORDER BY created_at DESC', [], (err, rows) => {
                    if (err) {
                        console.error('Error getting saved quizzes:', err);
                        resolve([]);
                    } else {
                        resolve(rows);
                    }
                });
            });
        } catch (error) {
            console.error('Error getting saved quizzes from DB:', error);
            return [];
        }
    }

    /**
     * Set language preference
     */
    setLanguage(language) {
        this.currentLanguage = language;
    }

    /**
     * Check if service is initialized
     */
    isReady() {
        return this.isInitialized;
    }

    /**
     * Analyze quiz performance patterns
     */
    analyzePerformance(quizHistory) {
        if (!quizHistory || quizHistory.length === 0) {
            return {
                totalQuizzes: 0,
                averageScore: 0,
                improvement: 'No data',
                strongAreas: [],
                weakAreas: []
            };
        }

        const totalQuizzes = quizHistory.length;
        const scores = quizHistory.map(q => (q.score_correct / q.score_total) * 100);
        const averageScore = scores.reduce((a, b) => a + b, 0) / scores.length;

        // Calculate improvement trend
        const recentScores = scores.slice(0, Math.min(5, scores.length));
        const olderScores = scores.slice(Math.min(5, scores.length));
        const recentAvg = recentScores.reduce((a, b) => a + b, 0) / recentScores.length;
        const olderAvg = olderScores.length > 0 ? olderScores.reduce((a, b) => a + b, 0) / olderScores.length : recentAvg;

        let improvement = 'Stable';
        if (recentAvg > olderAvg + 5) improvement = 'Improving';
        else if (recentAvg < olderAvg - 5) improvement = 'Declining';

        // Analyze by question type
        const typePerformance = {};
        quizHistory.forEach(quiz => {
            const type = quiz.question_type || 'Mixed';
            if (!typePerformance[type]) {
                typePerformance[type] = { total: 0, correct: 0, count: 0 };
            }
            typePerformance[type].total += quiz.score_total;
            typePerformance[type].correct += quiz.score_correct;
            typePerformance[type].count += 1;
        });

        const strongAreas = [];
        const weakAreas = [];

        Object.entries(typePerformance).forEach(([type, data]) => {
            const percentage = (data.correct / data.total) * 100;
            if (percentage >= 80) strongAreas.push(`${type} (${percentage.toFixed(1)}%)`);
            else if (percentage < 60) weakAreas.push(`${type} (${percentage.toFixed(1)}%)`);
        });

        return {
            totalQuizzes,
            averageScore: averageScore.toFixed(1),
            improvement,
            strongAreas,
            weakAreas,
            typePerformance,
            recentTrend: recentAvg.toFixed(1)
        };
    }

    /**
     * Analyze study patterns and habits
     */
    analyzeStudyPatterns(quizHistory, fileActivity) {
        const patterns = {
            studyFrequency: 'Unknown',
            preferredContent: 'Mixed',
            sessionLength: 'Unknown',
            consistency: 'Unknown'
        };

        if (!quizHistory || quizHistory.length === 0) return patterns;

        // Analyze study frequency
        const now = new Date();
        const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        const recentQuizzes = quizHistory.filter(q => new Date(q.created_at) > oneWeekAgo);

        if (recentQuizzes.length >= 5) patterns.studyFrequency = 'High (5+ quizzes/week)';
        else if (recentQuizzes.length >= 2) patterns.studyFrequency = 'Moderate (2-4 quizzes/week)';
        else if (recentQuizzes.length >= 1) patterns.studyFrequency = 'Low (1 quiz/week)';
        else patterns.studyFrequency = 'Inactive (no recent activity)';

        // Analyze preferred content types
        if (fileActivity && fileActivity.length > 0) {
            const fileTypes = fileActivity.map(f => f.file_type || 'document');
            const typeCount = {};
            fileTypes.forEach(type => {
                typeCount[type] = (typeCount[type] || 0) + 1;
            });
            const mostUsed = Object.entries(typeCount).sort((a, b) => b[1] - a[1])[0];
            patterns.preferredContent = mostUsed ? mostUsed[0] : 'Mixed';
        }

        // Analyze session duration patterns
        const durations = quizHistory.map(q => q.duration || 0).filter(d => d > 0);
        if (durations.length > 0) {
            const avgDuration = durations.reduce((a, b) => a + b, 0) / durations.length;
            if (avgDuration < 120) patterns.sessionLength = 'Quick (< 2 min)';
            else if (avgDuration < 300) patterns.sessionLength = 'Moderate (2-5 min)';
            else patterns.sessionLength = 'Extended (> 5 min)';
        }

        return patterns;
    }

    /**
     * Analyze time-based study patterns
     */
    analyzeTimePatterns(quizHistory) {
        if (!quizHistory || quizHistory.length === 0) {
            return { preferredTime: 'Unknown', consistency: 'No data' };
        }

        const timeSlots = { morning: 0, afternoon: 0, evening: 0, night: 0 };

        quizHistory.forEach(quiz => {
            const date = new Date(quiz.created_at);
            const hour = date.getHours();

            if (hour >= 6 && hour < 12) timeSlots.morning++;
            else if (hour >= 12 && hour < 17) timeSlots.afternoon++;
            else if (hour >= 17 && hour < 22) timeSlots.evening++;
            else timeSlots.night++;
        });

        const preferredTime = Object.entries(timeSlots)
            .sort((a, b) => b[1] - a[1])[0][0];

        return {
            preferredTime: preferredTime.charAt(0).toUpperCase() + preferredTime.slice(1),
            timeDistribution: timeSlots
        };
    }

    /**
     * Create concise English analysis prompt
     */
    createEnglishAnalysisPrompt(userData) {
        return `As an AI Study Assistant, provide a BRIEF analysis (max 150 words):

PERFORMANCE: ${userData.performance?.totalQuizzes || 0} quizzes, ${userData.performance?.averageScore || 0}% avg, trend: ${userData.performance?.improvement || 'stable'}
STRONG: ${userData.performance?.strongAreas?.join(', ') || 'None'}
WEAK: ${userData.performance?.weakAreas?.join(', ') || 'None'}
STUDY: ${userData.studyPatterns?.studyFrequency || 'Unknown'}, ${userData.studyPatterns?.sessionLength || 'Unknown'} sessions
FILES: ${userData.fileActivity?.length || 0} processed, ${userData.savedQuizzes?.length || 0} saved quizzes

Give CONCISE response with:
1. Quick performance summary (2 sentences max)
2. Top 2 strengths and 1 main weakness
3. 1-2 specific actionable recommendations
4. Brief encouragement (1 sentence)

Be direct, brief, and actionable. No long explanations.`;
    }

    /**
     * Create concise Arabic analysis prompt
     */
    createArabicAnalysisPrompt(userData) {
        return `كمساعد ذكي للدراسة، قدم تحليلاً مختصراً (150 كلمة كحد أقصى):

الأداء: ${userData.performance?.totalQuizzes || 0} اختبار، ${userData.performance?.averageScore || 0}% متوسط، الاتجاه: ${userData.performance?.improvement || 'مستقر'}
القوة: ${userData.performance?.strongAreas?.join(', ') || 'لا يوجد'}
الضعف: ${userData.performance?.weakAreas?.join(', ') || 'لا يوجد'}
الدراسة: ${userData.studyPatterns?.studyFrequency || 'غير معروف'}، جلسات ${userData.studyPatterns?.sessionLength || 'غير معروف'}
الملفات: ${userData.fileActivity?.length || 0} معالج، ${userData.savedQuizzes?.length || 0} اختبار محفوظ

قدم رداً مختصراً يتضمن:
1. ملخص سريع للأداء (جملتان كحد أقصى)
2. أهم نقطتي قوة ونقطة ضعف واحدة
3. توصية أو توصيتان قابلتان للتطبيق
4. تشجيع مختصر (جملة واحدة)

كن مباشراً ومختصراً وقابلاً للتطبيق. لا تقدم شروحات طويلة.`;
    }

    /**
     * Enhance message to encourage concise responses
     */
    enhanceMessageForConciseness(message, language) {
        const conciseInstruction = language === 'ar' ?
            'أجب بإيجاز ووضوح (100 كلمة كحد أقصى). كن مباشراً ومفيداً. ' :
            'Answer briefly and clearly (max 100 words). Be direct and helpful. ';

        return conciseInstruction + message;
    }

    /**
     * Generate action buttons based on AI response content
     */
    async generateActionButtons(response, language) {
        const buttons = [];
        const lowerResponse = response.toLowerCase();

        // Quiz-related buttons
        if (lowerResponse.includes('quiz') || lowerResponse.includes('test') || lowerResponse.includes('practice')) {
            buttons.push({
                text: language === 'ar' ? '🎯 ابدأ اختبار' : '🎯 Take Quiz',
                action: 'navigate',
                target: 'question-generator',
                type: 'primary'
            });
        }

        // Study recommendations
        if (lowerResponse.includes('study') || lowerResponse.includes('learn') || lowerResponse.includes('improve')) {
            buttons.push({
                text: language === 'ar' ? '📚 نصائح دراسية' : '📚 Study Tips',
                action: 'chat',
                message: language === 'ar' ? 'أعطني نصائح دراسية محددة' : 'Give me specific study tips',
                type: 'secondary'
            });
        }

        // Saved quizzes
        if (lowerResponse.includes('saved') || lowerResponse.includes('previous') || lowerResponse.includes('retake')) {
            const savedQuizzes = await this.getSavedQuizzesFromDB();
            if (savedQuizzes.length > 0) {
                buttons.push({
                    text: language === 'ar' ? '📋 الاختبارات المحفوظة' : '📋 Saved Quizzes',
                    action: 'navigate',
                    target: 'saved-quizzes',
                    type: 'secondary'
                });
            }
        }

        // Progress tracking
        if (lowerResponse.includes('progress') || lowerResponse.includes('performance') || lowerResponse.includes('improvement')) {
            buttons.push({
                text: language === 'ar' ? '📊 تقرير مفصل' : '📊 Detailed Report',
                action: 'chat',
                message: language === 'ar' ? 'أظهر تقريراً مفصلاً عن تقدمي' : 'Show detailed progress report',
                type: 'secondary'
            });
        }

        // File upload suggestion
        if (lowerResponse.includes('material') || lowerResponse.includes('content') || lowerResponse.includes('document')) {
            buttons.push({
                text: language === 'ar' ? '📄 رفع ملف' : '📄 Upload File',
                action: 'navigate',
                target: 'file-upload',
                type: 'secondary'
            });
        }

        // Mind map suggestion
        if (lowerResponse.includes('visual') || lowerResponse.includes('map') || lowerResponse.includes('diagram')) {
            buttons.push({
                text: language === 'ar' ? '🧠 خريطة ذهنية' : '🧠 Mind Map',
                action: 'navigate',
                target: 'mind-map',
                type: 'secondary'
            });
        }

        // Limit to 3 buttons max for clean UI
        return buttons.slice(0, 3);
    }

    /**
     * Get instant response for common queries (no AI delay)
     */
    getInstantResponse(message, language) {
        const lowerMessage = message.toLowerCase();

        // Common greetings
        if (lowerMessage.match(/^(hi|hello|hey|good morning|good afternoon|good evening)$/)) {
            return language === 'ar' ?
                'مرحباً! كيف يمكنني مساعدتك في دراستك اليوم؟' :
                'Hi! How can I help you with your studies today?';
        }

        // Quick progress requests
        if (lowerMessage.includes('how am i doing') || lowerMessage.includes('my progress')) {
            return language === 'ar' ?
                'أنت تحرز تقدماً جيداً! لديك نشاط دراسي منتظم. هل تريد تقريراً مفصلاً؟' :
                'You\'re making good progress! You have consistent study activity. Want a detailed report?';
        }

        // Quiz requests
        if (lowerMessage.includes('quiz') || lowerMessage.includes('test') || lowerMessage.includes('practice')) {
            return language === 'ar' ?
                'ممتاز! الممارسة المنتظمة هي مفتاح النجاح. ابدأ اختباراً جديداً أو راجع الاختبارات المحفوظة.' :
                'Great! Regular practice is key to success. Start a new quiz or review saved ones.';
        }

        // Study help
        if (lowerMessage.includes('help') || lowerMessage.includes('study tips') || lowerMessage.includes('advice')) {
            return language === 'ar' ?
                'نصائح سريعة: راجع الأخطاء، اقرأ بتركيز، مارس يومياً. هل تريد نصائح محددة؟' :
                'Quick tips: Review mistakes, read actively, practice daily. Want specific advice?';
        }

        // File/content questions
        if (lowerMessage.includes('upload') || lowerMessage.includes('file') || lowerMessage.includes('document')) {
            return language === 'ar' ?
                'رفع الملفات يساعدني في فهم ما تدرسه. ارفع ملفاً لأحلل المحتوى وأقدم اختبارات مخصصة.' :
                'Uploading files helps me understand what you\'re studying. Upload a file for content analysis and custom quizzes.';
        }

        // Mind map requests
        if (lowerMessage.includes('mind map') || lowerMessage.includes('visual') || lowerMessage.includes('diagram')) {
            return language === 'ar' ?
                'الخرائط الذهنية رائعة للتعلم البصري! أنشئ خريطة ذهنية لتنظيم أفكارك.' :
                'Mind maps are great for visual learning! Create a mind map to organize your thoughts.';
        }

        return null; // No instant response available
    }

    /**
     * Get AI response with enhanced prompting
     */
    async getAIResponse(message, language) {
        const enhancedMessage = this.enhanceMessageForConciseness(message, language);
        return await this.geminiService.chatWithAI(enhancedMessage, this.studentId, language);
    }

    /**
     * Get fallback response when AI is slow/unavailable
     */
    getFallbackResponse(message, language) {
        const lowerMessage = message.toLowerCase();

        if (lowerMessage.includes('progress') || lowerMessage.includes('performance')) {
            return language === 'ar' ?
                'أنت تحرز تقدماً مستمراً! استمر في الممارسة المنتظمة وراجع الأخطاء لتحسين الأداء.' :
                'You\'re making steady progress! Keep practicing regularly and review mistakes to improve performance.';
        }

        if (lowerMessage.includes('quiz') || lowerMessage.includes('test')) {
            return language === 'ar' ?
                'الاختبارات المنتظمة تقوي الذاكرة. جرب اختباراً جديداً أو راجع الاختبارات المحفوظة.' :
                'Regular quizzes strengthen memory. Try a new quiz or review saved ones.';
        }

        if (lowerMessage.includes('study') || lowerMessage.includes('learn')) {
            return language === 'ar' ?
                'للدراسة الفعالة: ركز على نقاط الضعف، راجع بانتظام، واستخدم مصادر متنوعة.' :
                'For effective studying: Focus on weak areas, review regularly, and use diverse sources.';
        }

        // Generic helpful response
        return language === 'ar' ?
            'أنا هنا لمساعدتك! استخدم الأزرار أدناه للوصول السريع للميزات.' :
            'I\'m here to help! Use the buttons below for quick access to features.';
    }

    /**
     * Get error response
     */
    getErrorResponse(language) {
        return {
            success: true,
            response: language === 'ar' ?
                'عذراً، حدث خطأ مؤقت. جرب مرة أخرى أو استخدم الأزرار للوصول السريع.' :
                'Sorry, temporary error occurred. Try again or use buttons for quick access.',
            actionButtons: [
                {
                    text: language === 'ar' ? '🎯 ابدأ اختبار' : '🎯 Take Quiz',
                    action: 'navigate',
                    target: 'question-generator',
                    type: 'primary'
                },
                {
                    text: language === 'ar' ? '📊 التقدم' : '📊 Progress',
                    action: 'chat',
                    message: language === 'ar' ? 'أظهر تقدمي' : 'Show my progress',
                    type: 'secondary'
                }
            ],
            timestamp: Date.now(),
            source: 'error'
        };
    }
}

module.exports = AIChatService;
