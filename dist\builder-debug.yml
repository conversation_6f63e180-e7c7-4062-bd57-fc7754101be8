x64:
  firstOrDefaultFilePatterns:
    - '!**/node_modules'
    - '!assets{,/**/*}'
    - '!dist{,/**/*}'
    - src/**/*
    - data/**/*
    - tessdata/**/*
    - assets/**/*
    - external-tools/**/*
    - gemini-cli-main/**/*
    - Lib/**/*
    - Scripts/**/*
    - logs/**/*
    - uploads/**/*
    - temp/**/*
    - node_modules/**/*
    - package.json
    - simple_extraction_service_fixed.py
    - requirements.txt
    - external-tools-manager.js
    - '*.md'
    - '!**/*.{iml,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,suo,xproj,cc,d.ts,mk,a,o,forge-meta,pdb}'
    - '!**/._*'
    - '!**/electron-builder.{yaml,yml,json,json5,toml,ts}'
    - '!**/{.git,.hg,.svn,CVS,RCS,SCCS,__pycache__,.DS_Store,thumbs.db,.gitignore,.gitkeep,.gitattributes,.npmignore,.idea,.vs,.flowconfig,.jshintrc,.eslintrc,.circleci,.yarn-integrity,.yarn-metadata.json,yarn-error.log,yarn.lock,package-lock.json,npm-debug.log,appveyor.yml,.travis.yml,circle.yml,.nyc_output,.husky,.github,electron-builder.env}'
    - '!.yarn{,/**/*}'
    - '!.editorconfig'
    - '!.yarnrc.yml'
  nodeModuleFilePatterns:
    - '**/*'
    - src/**/*
    - data/**/*
    - tessdata/**/*
    - assets/**/*
    - external-tools/**/*
    - gemini-cli-main/**/*
    - Lib/**/*
    - Scripts/**/*
    - logs/**/*
    - uploads/**/*
    - temp/**/*
    - node_modules/**/*
    - package.json
    - simple_extraction_service_fixed.py
    - requirements.txt
    - external-tools-manager.js
    - '*.md'
