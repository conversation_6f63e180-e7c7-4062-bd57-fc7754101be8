// Load environment variables if needed
require('dotenv').config();

// Read directly from the .env file as a backup in case dotenv fails due to BOM
const fs = require('fs');
const path = require('path');
const envPath = path.resolve(__dirname, '../.env');

// Function to load environment variables from file if not already set
function loadEnvFromFile() {
  try {
    if (fs.existsSync(envPath)) {
      const envContent = fs.readFileSync(envPath, 'utf8');
      // Process each line
      envContent.split('\n').forEach(line => {
        // Skip comments and empty lines
        if (line.trim() && !line.startsWith('#')) {
          const equalSignPos = line.indexOf('=');
          if (equalSignPos !== -1) {
            const key = line.substring(0, equalSignPos).trim();
            const value = line.substring(equalSignPos + 1).trim();
            
            // Only set if not already in process.env
            if (!process.env[key]) {
              process.env[key] = value;
            }
          }
        }
      });
      console.log('Loaded environment variables from file directly');
    } else {
      console.log('.env file not found at:', envPath);
    }
  } catch (error) {
    console.error('Error loading environment variables from file:', error);
  }
}

// Load environment variables from file if needed
loadEnvFromFile();

console.log('TELEGRAM_BOT_TOKEN length:', process.env.TELEGRAM_BOT_TOKEN ? process.env.TELEGRAM_BOT_TOKEN.length : 'not set');
console.log('TELEGRAM_TOKEN length:', process.env.TELEGRAM_TOKEN ? process.env.TELEGRAM_TOKEN.length : 'not set');

// Get environment variables
const questionsPerPage = parseInt(process.env.QUESTIONS_PER_PAGE || '5', 10);
const imageQuestionsCount = parseInt(process.env.IMAGE_QUESTIONS_COUNT || '5', 10);
const maxConcurrentRequests = parseInt(process.env.MAX_CONCURRENT_REQUESTS || '8', 10);
const maxRequestsPerModel = parseInt(process.env.MAX_REQUESTS_PER_MODEL || '2', 10);
const maxRequestsPerUser = parseInt(process.env.MAX_REQUESTS_PER_USER || '2', 10);
const cacheTTL = parseInt(process.env.CACHE_TTL || '86400', 10); // 24 hours in seconds
const maxCacheSize = parseInt(process.env.MAX_CACHE_SIZE || '500', 10); // Maximum cache entries
const apiTimeout = parseInt(process.env.API_TIMEOUT || '180000', 10); // 3 minutes timeout for API calls

// Load models from custom configuration or use defaults
function loadModels() {
  const customModelsPath = path.join(__dirname, 'config', 'models.json');

  // Try to load custom models first
  if (fs.existsSync(customModelsPath)) {
    try {
      const customModels = JSON.parse(fs.readFileSync(customModelsPath, 'utf8'));
      if (customModels.length > 0) {
        console.log(`Loaded ${customModels.length} custom models from models.json`);
        return customModels.map(model => model.id);
      }
    } catch (error) {
      console.warn('Error loading custom models:', error.message);
    }
  }

  // No fallback models - user must configure their own models
  console.log('No custom models found - user must add models through the UI');
  return [];
}

const models = loadModels();

// Configure supported document types
const supportedFileTypes = [
  'application/pdf',
  'text/plain',
  'image/jpeg',
  'image/png', 
  'image/jpg',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // docx
  'application/msword', // doc
  'application/vnd.openxmlformats-officedocument.presentationml.presentation', // pptx
  'application/vnd.ms-powerpoint', // ppt
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // xlsx
  'application/vnd.ms-excel', // xls
  'text/rtf', // RTF
  'text/html', // HTML
  'application/vnd.oasis.opendocument.text', // ODT
  'application/vnd.oasis.opendocument.presentation' // ODP
];

// Create configuration object from environment variables
const config = {
  token: process.env.TELEGRAM_BOT_TOKEN || process.env.TELEGRAM_TOKEN,
  admins: (process.env.ADMIN_IDS || '').split(',').filter(id => id.trim() !== ''),
  questionsPerPage,
  imageQuestionsCount,
  fileUploadsPerDay: parseInt(process.env.FILE_UPLOADS_PER_DAY || '5'),
  cacheMaxAgeHours: parseInt(process.env.CACHE_MAX_AGE_HOURS || '72'),
  cleanupIntervalHours: parseInt(process.env.CLEANUP_INTERVAL_HOURS || '24'),
  rateLimits: {
    requestsPerDay: parseInt(process.env.REQUESTS_PER_DAY || '20'),
    resetIntervalHours: parseInt(process.env.RESET_INTERVAL_HOURS || '24')
  },
  // Concurrency management settings
  maxConcurrentRequests,
  maxRequestsPerModel,
  maxRequestsPerUser,
  // Always use OpenRouter API for these models
  defaultApiUrl: 'https://openrouter.ai/api/v1/chat/completions',
  models,
  requestTimeout: parseInt(process.env.REQUEST_TIMEOUT || '60000'),
  apiKey: process.env.API_KEY || '',
  // For backward compatibility
  apiKeys: [process.env.API_KEY || ''].filter(key => key.trim() !== ''),
  // Required channel ID that users must join
  requiredChannelId: process.env.REQUIRED_CHANNEL_ID || null,
  cacheTTL,
  maxCacheSize,
  apiTimeout,
  supportedFileTypes,
  
  // Performance optimization settings
  extractionCacheTTL: 3600000, // 1 hour in milliseconds
  extractionCacheSize: 50,
  extractionParallelWorkers: 4, // Number of parallel extraction workers
  extractionTimeoutMs: 600000, // 10 minutes for extraction
  
  // Limits and quotas
  maxDailyFileUploads: 10, // Maximum files per user per day
  maxFileSizeMB: 1000, // Maximum file size in MB (increased to 1000MB/1GB)
  maxImageSize: 1000 * 1024 * 1024, // 1000MB in bytes
  maxDocumentSize: 1000 * 1024 * 1024, // 1000MB in bytes
  
  // Debug options
  debug: process.env.DEBUG_MODE === 'true',
  logLevel: process.env.LOG_LEVEL || 'info'
};

console.log('Using token:', config.token ? `${config.token.substring(0, 8)}...` : 'not set');
console.log('Admin IDs:', config.admins);
console.log('API Key available:', config.apiKey ? 'Yes' : 'No');
console.log('Models:', config.models.length);

module.exports = config; 