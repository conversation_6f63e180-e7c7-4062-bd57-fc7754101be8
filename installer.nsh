; Custom NSIS installer script for MCQ & TF Question Generator
; This script handles additional setup tasks during installation

!macro customInit
  ; Check for required dependencies
  DetailPrint "Checking system requirements..."
  
  ; Check for Visual C++ Redistributable
  ReadRegStr $0 HKLM "SOFTWARE\Microsoft\VisualStudio\14.0\VC\Runtimes\x64" "Installed"
  ${If} $0 != "1"
    DetailPrint "Visual C++ Redistributable may be required for some features"
  ${EndIf}
  
  ; Create application data directories
  CreateDirectory "$APPDATA\MCQ-TF-7models"
  CreateDirectory "$APPDATA\MCQ-TF-7models\logs"
  CreateDirectory "$APPDATA\MCQ-TF-7models\uploads"
  CreateDirectory "$APPDATA\MCQ-TF-7models\temp"
  CreateDirectory "$APPDATA\MCQ-TF-7models\data"
  
  DetailPrint "Application directories created"
!macroend

!macro customInstall
  ; Set permissions for application directories
  DetailPrint "Setting up application permissions..."
  
  ; Ensure the application can write to its directories
  AccessControl::GrantOnFile "$INSTDIR" "(BU)" "FullAccess"
  AccessControl::GrantOnFile "$APPDATA\MCQ-TF-7models" "(BU)" "FullAccess"
  
  ; Create registry entries for file associations (optional)
  WriteRegStr HKCU "SOFTWARE\Classes\.mcq" "" "MCQFile"
  WriteRegStr HKCU "SOFTWARE\Classes\MCQFile" "" "MCQ Question File"
  WriteRegStr HKCU "SOFTWARE\Classes\MCQFile\shell\open\command" "" '"$INSTDIR\MCQ TF-7models.exe" "%1"'
  
  DetailPrint "File associations configured"
  
  ; Copy additional resources if needed
  ${If} ${FileExists} "$INSTDIR\external-tools\tesseract\INSTALLATION.md"
    DetailPrint "External tools configured"
  ${EndIf}
!macroend

!macro customUnInstall
  ; Clean up application data (optional - user choice)
  MessageBox MB_YESNO "Do you want to remove all application data including saved quizzes and settings?" IDNO skip_data_removal
  
  RMDir /r "$APPDATA\MCQ-TF-7models"
  DetailPrint "Application data removed"
  
  skip_data_removal:
  
  ; Remove registry entries
  DeleteRegKey HKCU "SOFTWARE\Classes\.mcq"
  DeleteRegKey HKCU "SOFTWARE\Classes\MCQFile"
  
  DetailPrint "Registry entries cleaned up"
!macroend

; Custom page for additional options
!macro customWelcomePage
  ; Add information about the application
  !define MUI_WELCOMEPAGE_TITLE "Welcome to MCQ & TF Question Generator Setup"
  !define MUI_WELCOMEPAGE_TEXT "This application helps students generate multiple-choice and true/false questions using AI technology.$\r$\n$\r$\nFeatures include:$\r$\n• AI-powered question generation$\r$\n• Text extraction from documents$\r$\n• Mind mapping capabilities$\r$\n• Web search integration$\r$\n• Interactive quiz interface$\r$\n$\r$\nClick Next to continue with the installation."
!macroend

; Custom finish page
!macro customFinishPage
  !define MUI_FINISHPAGE_TITLE "Installation Complete"
  !define MUI_FINISHPAGE_TEXT "MCQ & TF Question Generator has been successfully installed.$\r$\n$\r$\nNote: Some features may require additional setup:$\r$\n• Google authentication for AI services$\r$\n• Internet connection for web search$\r$\n• Tesseract OCR for advanced text extraction$\r$\n$\r$\nClick Finish to complete the installation."
  !define MUI_FINISHPAGE_RUN "$INSTDIR\MCQ TF-7models.exe"
  !define MUI_FINISHPAGE_RUN_TEXT "Launch MCQ & TF Question Generator"
!macroend
