# MCQ & TF Question Generator - Build Verification Script
# This script verifies that all components of the Windows build are properly included

Write-Host "=== MCQ & TF Question Generator Build Verification ===" -ForegroundColor Green
Write-Host ""

$buildPath = "dist\win-unpacked"
$installerPath = "dist\MCQ & TF Question Generator-1.0.0-x64.exe"

# Check if build directory exists
if (-not (Test-Path $buildPath)) {
    Write-Host "❌ Build directory not found: $buildPath" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Build directory found: $buildPath" -ForegroundColor Green

# Check main executable
$mainExe = Join-Path $buildPath "electron.exe"
if (Test-Path $mainExe) {
    $exeInfo = Get-ItemProperty $mainExe
    Write-Host "✅ Main executable: $($exeInfo.Name) ($([math]::Round($exeInfo.Length/1MB,1)) MB)" -ForegroundColor Green
} else {
    Write-Host "❌ Main executable not found" -ForegroundColor Red
}

# Check installer
if (Test-Path $installerPath) {
    $installerInfo = Get-ItemProperty $installerPath
    Write-Host "✅ Installer: $($installerInfo.Name) ($([math]::Round($installerInfo.Length/1KB,1)) KB)" -ForegroundColor Green
} else {
    Write-Host "❌ Installer not found" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== Checking Bundled Dependencies ===" -ForegroundColor Yellow

# Check bundled dependencies in resources/app
$appPath = Join-Path $buildPath "resources\app"

# Check Node modules
$nodeModules = Join-Path $appPath "node_modules"
if (Test-Path $nodeModules) {
    $moduleCount = (Get-ChildItem $nodeModules -Directory).Count
    $moduleSize = [math]::Round((Get-ChildItem $nodeModules -Recurse -File | Measure-Object -Property Length -Sum).Sum / 1MB, 1)
    Write-Host "✅ Node Modules: $moduleCount packages ($moduleSize MB)" -ForegroundColor Green
} else {
    Write-Host "❌ Node modules not found" -ForegroundColor Red
}

# Check Gemini CLI
$geminiPath = Join-Path $appPath "gemini-cli-main"
if (Test-Path $geminiPath) {
    Write-Host "✅ Gemini CLI found" -ForegroundColor Green
} else {
    Write-Host "❌ Gemini CLI not found" -ForegroundColor Red
}

# Check Tesseract data
$tessdata = Join-Path $appPath "tessdata"
if (Test-Path $tessdata) {
    $tessFiles = (Get-ChildItem $tessdata -File).Count
    Write-Host "✅ Tesseract Data: $tessFiles language files" -ForegroundColor Green
} else {
    Write-Host "❌ Tesseract data not found" -ForegroundColor Red
}

# Check resources
$resourcesPath = Join-Path $buildPath "resources"
if (Test-Path $resourcesPath) {
    $resourcesSize = [math]::Round((Get-ChildItem $resourcesPath -Recurse -File | Measure-Object -Property Length -Sum).Sum / 1MB, 1)
    Write-Host "✅ Resources: $resourcesSize MB" -ForegroundColor Green
} else {
    Write-Host "❌ Resources directory not found" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== Checking Critical Files ===" -ForegroundColor Yellow

# Critical DLLs and files
$criticalFiles = @(
    "chrome_100_percent.pak",
    "chrome_200_percent.pak", 
    "resources.pak",
    "icudtl.dat",
    "snapshot_blob.bin",
    "v8_context_snapshot.bin"
)

foreach ($file in $criticalFiles) {
    $filePath = Join-Path $buildPath $file
    if (Test-Path $filePath) {
        Write-Host "✅ $file" -ForegroundColor Green
    } else {
        Write-Host "❌ $file missing" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "=== Build Summary ===" -ForegroundColor Yellow

# Calculate total size
$totalSize = [math]::Round((Get-ChildItem $buildPath -Recurse -File | Measure-Object -Property Length -Sum).Sum / 1MB, 1)
$fileCount = (Get-ChildItem $buildPath -Recurse -File).Count

Write-Host "📊 Total Build Size: $totalSize MB" -ForegroundColor Cyan
Write-Host "📊 Total Files: $fileCount" -ForegroundColor Cyan

# Check if app can start (quick test)
Write-Host ""
Write-Host "=== Quick Launch Test ===" -ForegroundColor Yellow

try {
    $process = Start-Process -FilePath $mainExe -PassThru -WindowStyle Hidden
    Start-Sleep -Seconds 3
    
    if (-not $process.HasExited) {
        Write-Host "✅ Application launches successfully" -ForegroundColor Green
        $process.Kill()
        $process.WaitForExit()
    } else {
        Write-Host "❌ Application failed to start (exit code: $($process.ExitCode))" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Failed to launch application: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== Verification Complete ===" -ForegroundColor Green
Write-Host "Build is ready for distribution!" -ForegroundColor Green
