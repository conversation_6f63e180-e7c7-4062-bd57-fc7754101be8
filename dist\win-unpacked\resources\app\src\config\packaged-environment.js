const path = require('path');
const fs = require('fs');
const { app } = require('electron');

/**
 * Configuration for packaged environment
 * Handles paths and resources when running as an executable
 */

class PackagedEnvironment {
    constructor() {
        this.isPackaged = app ? app.isPackaged : false;
        this.appPath = this.getAppPath();
        this.resourcesPath = this.getResourcesPath();
        this.userDataPath = this.getUserDataPath();
        
        // Initialize paths
        this.initializePaths();
    }
    
    getAppPath() {
        if (this.isPackaged && app) {
            return app.getAppPath();
        }
        return process.cwd();
    }
    
    getResourcesPath() {
        if (this.isPackaged && app) {
            return process.resourcesPath;
        }
        return process.cwd();
    }
    
    getUserDataPath() {
        if (app) {
            return app.getPath('userData');
        }
        return path.join(require('os').homedir(), '.mcq-tf-7models');
    }
    
    initializePaths() {
        // Create necessary directories
        const dirs = [
            this.getUserDataPath(),
            path.join(this.getUserDataPath(), 'logs'),
            path.join(this.getUserDataPath(), 'uploads'),
            path.join(this.getUserDataPath(), 'temp'),
            path.join(this.getUserDataPath(), 'data')
        ];
        
        dirs.forEach(dir => {
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }
        });
    }
    
    /**
     * Get the path to the Python extraction service
     */
    getPythonServicePath() {
        if (this.isPackaged) {
            return path.join(this.resourcesPath, 'simple_extraction_service_fixed.py');
        }
        return path.join(this.appPath, 'simple_extraction_service_fixed.py');
    }
    
    /**
     * Get the path to Python executable or Scripts directory
     */
    getPythonPath() {
        const possiblePaths = [
            // Bundled Python environment
            path.join(this.resourcesPath, 'Scripts', 'python.exe'),
            path.join(this.appPath, 'Scripts', 'python.exe'),
            // System Python
            'python',
            'python3'
        ];
        
        for (const pythonPath of possiblePaths) {
            if (pythonPath.includes('python.exe') && fs.existsSync(pythonPath)) {
                return pythonPath;
            } else if (!pythonPath.includes('.exe')) {
                // For system Python, we'll try it and see if it works
                return pythonPath;
            }
        }
        
        return 'python'; // Fallback
    }
    
    /**
     * Get the path to Gemini CLI
     */
    getGeminiPath() {
        const possiblePaths = [
            // Bundled Gemini CLI
            path.join(this.resourcesPath, 'external-tools', 'gemini-cli', 'gemini-cli-main', 'bin', 'gemini.js'),
            path.join(this.appPath, 'external-tools', 'gemini-cli', 'gemini-cli-main', 'bin', 'gemini.js'),
            path.join(this.resourcesPath, 'gemini-cli-main', 'bin', 'gemini.js'),
            path.join(this.appPath, 'gemini-cli-main', 'bin', 'gemini.js'),
            // System Gemini CLI
            'gemini'
        ];
        
        for (const geminiPath of possiblePaths) {
            if (geminiPath.includes('.js') && fs.existsSync(geminiPath)) {
                return 'node'; // We'll run it with node
            } else if (!geminiPath.includes('.js')) {
                return geminiPath; // System gemini
            }
        }
        
        return 'gemini'; // Fallback
    }
    
    /**
     * Get arguments for running Gemini CLI
     */
    getGeminiArgs(originalArgs = []) {
        const geminiPath = this.getGeminiPath();
        
        if (geminiPath === 'node') {
            // Find the actual JS file
            const possiblePaths = [
                path.join(this.resourcesPath, 'external-tools', 'gemini-cli', 'gemini-cli-main', 'bin', 'gemini.js'),
                path.join(this.appPath, 'external-tools', 'gemini-cli', 'gemini-cli-main', 'bin', 'gemini.js'),
                path.join(this.resourcesPath, 'gemini-cli-main', 'bin', 'gemini.js'),
                path.join(this.appPath, 'gemini-cli-main', 'bin', 'gemini.js')
            ];
            
            for (const jsPath of possiblePaths) {
                if (fs.existsSync(jsPath)) {
                    return [jsPath, ...originalArgs];
                }
            }
        }
        
        // For system gemini or PowerShell wrapper
        const isWindows = require('os').platform() === 'win32';
        if (isWindows && geminiPath === 'gemini') {
            return ['-Command', 'gemini', ...originalArgs];
        }
        
        return originalArgs;
    }
    
    /**
     * Get the path to Tesseract executable
     */
    getTesseractPath() {
        const possiblePaths = [
            // Bundled Tesseract
            path.join(this.resourcesPath, 'external-tools', 'tesseract', 'tesseract.exe'),
            path.join(this.appPath, 'external-tools', 'tesseract', 'tesseract.exe'),
            // System Tesseract
            'C:\\Program Files\\Tesseract-OCR\\tesseract.exe',
            'C:\\Program Files (x86)\\Tesseract-OCR\\tesseract.exe',
            'tesseract'
        ];
        
        for (const tesseractPath of possiblePaths) {
            if (tesseractPath.includes('.exe') && fs.existsSync(tesseractPath)) {
                return tesseractPath;
            } else if (!tesseractPath.includes('.exe')) {
                return tesseractPath; // System tesseract
            }
        }
        
        return 'tesseract'; // Fallback
    }
    
    /**
     * Get the path to tessdata directory
     */
    getTessdataPath() {
        const possiblePaths = [
            path.join(this.resourcesPath, 'external-tools', 'tesseract', 'tessdata'),
            path.join(this.appPath, 'external-tools', 'tesseract', 'tessdata'),
            path.join(this.resourcesPath, 'tessdata'),
            path.join(this.appPath, 'tessdata')
        ];
        
        for (const tessdataPath of possiblePaths) {
            if (fs.existsSync(tessdataPath)) {
                return tessdataPath;
            }
        }
        
        return null;
    }
    
    /**
     * Get database path for the packaged environment
     */
    getDatabasePath() {
        return path.join(this.getUserDataPath(), 'data', 'app.db');
    }
    
    /**
     * Get logs directory
     */
    getLogsPath() {
        return path.join(this.getUserDataPath(), 'logs');
    }
    
    /**
     * Get uploads directory
     */
    getUploadsPath() {
        return path.join(this.getUserDataPath(), 'uploads');
    }
    
    /**
     * Get temp directory
     */
    getTempPath() {
        return path.join(this.getUserDataPath(), 'temp');
    }
    
    /**
     * Check if running in development mode
     */
    isDevelopment() {
        return !this.isPackaged;
    }
    
    /**
     * Get configuration for external tools
     */
    getExternalToolsConfig() {
        return {
            python: {
                executable: this.getPythonPath(),
                service: this.getPythonServicePath()
            },
            gemini: {
                executable: this.getGeminiPath(),
                args: this.getGeminiArgs.bind(this)
            },
            tesseract: {
                executable: this.getTesseractPath(),
                tessdata: this.getTessdataPath()
            }
        };
    }
}

// Export singleton instance
const packagedEnvironment = new PackagedEnvironment();
module.exports = packagedEnvironment;
