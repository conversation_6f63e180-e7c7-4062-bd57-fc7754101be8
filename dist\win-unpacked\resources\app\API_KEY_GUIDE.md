# 🔑 API Key Management Guide

## Current API Key Information

**Current API Key:** `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`

**Status:** ✅ **WORKING** (Privacy settings enabled, models functional)

**Provider:** OpenRouter.ai

**Models Supported:** All free models including DeepSeek, Gemini, Mistral, etc.

---

## 🔄 How to Change the API Key

### **Method 1: Update .env File (Recommended)**

1. **Open the `.env` file** in your project root directory
2. **Find the line:** `API_KEY=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`
3. **Replace with your new key:**
   ```env
   API_KEY=your-new-api-key-here
   ```
4. **Save the file**
5. **Restart the application** for changes to take effect

### **Method 2: Environment Variable**

Set the API key as a system environment variable:

**Windows:**
```cmd
set API_KEY=your-new-api-key-here
```

**Linux/Mac:**
```bash
export API_KEY=your-new-api-key-here
```

### **Method 3: Direct Code Update**

If you need to hardcode the key (not recommended for security):

1. Open `src/config/config.js`
2. Find the `apiKey` configuration
3. Update the value directly

---

## 🆕 Getting a New OpenRouter API Key

### **Step 1: Visit OpenRouter**
Go to: https://openrouter.ai/

### **Step 2: Create Account/Login**
- Sign up for a new account or login to existing account
- Verify your email if required

### **Step 3: Generate API Key**
1. Go to **Settings** → **API Keys**
2. Click **"Create API Key"**
3. Give it a descriptive name (e.g., "MCQ Generator App")
4. Copy the generated key (starts with `sk-or-v1-`)

### **Step 4: Configure Privacy Settings**
⚠️ **IMPORTANT:** Enable these settings for free models to work:

1. Go to **Settings** → **Privacy**
2. **Enable "Prompt Training"** ✅
3. **Set Data Policy** to allow model access
4. **Save changes**

Without these settings, you'll get 404 errors!

---

## 🔧 API Key Format

**Valid OpenRouter API Key Format:**
```
sk-or-v1-[32-character-hex-string]
```

**Example:**
```
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
```

**Length:** 73 characters total
- Prefix: `sk-or-v1-` (9 characters)
- Key: 64 hex characters

---

## ✅ Testing Your New API Key

### **Quick Test Method:**

1. **Update the API key** in `.env`
2. **Restart the application**
3. **Try generating questions** with any content
4. **Check the logs** for success/error messages

### **Advanced Test Method:**

Run the verification script:
```bash
npx electron verification-simulation.js
```

This will test all models and confirm the API key is working.

---

## 🚨 Troubleshooting

### **Common Issues:**

#### **404 Error - "No endpoints found matching your data policy"**
**Solution:** Enable prompt training in OpenRouter privacy settings

#### **401 Error - "Unauthorized"**
**Solution:** Check if API key is correct and properly formatted

#### **403 Error - "Forbidden"**
**Solution:** Verify account has access to the models you're trying to use

#### **Rate Limit Errors**
**Solution:** Wait a few minutes or upgrade your OpenRouter plan

### **Verification Steps:**

1. ✅ **API Key Format:** Starts with `sk-or-v1-` and is 73 characters
2. ✅ **Privacy Settings:** Prompt training enabled on OpenRouter
3. ✅ **File Updated:** `.env` file contains the new key
4. ✅ **App Restarted:** Application restarted after key change
5. ✅ **Models Available:** At least one model responds successfully

---

## 🔒 Security Best Practices

### **DO:**
- ✅ Keep API keys in `.env` file (not tracked by git)
- ✅ Use environment variables in production
- ✅ Regenerate keys periodically
- ✅ Monitor usage on OpenRouter dashboard

### **DON'T:**
- ❌ Commit API keys to version control
- ❌ Share API keys in public forums
- ❌ Hardcode keys in source code
- ❌ Use the same key across multiple projects

---

## 📊 Current Working Models

With your current API key, these models are confirmed working:

1. ✅ **DeepSeek Chat V3** - `deepseek/deepseek-chat-v3-0324:free`
2. ✅ **Gemini 2.0 Flash** - `google/gemini-2.0-flash-exp:free`
3. ✅ **Mistral Devstral** - `mistralai/devstral-small:free`
4. ✅ **Gemma 3 27B** - `google/gemma-3-27b-it:free`
5. ✅ **Llama 3.1 Nemotron** - `nvidia/llama-3.1-nemotron-ultra-253b-v1:free`

---

## 🆘 Need Help?

If you encounter issues after changing the API key:

1. **Check the application logs** for specific error messages
2. **Verify the key format** matches the expected pattern
3. **Confirm privacy settings** are enabled on OpenRouter
4. **Test with a simple request** first
5. **Contact OpenRouter support** if the key itself has issues

---

**Last Updated:** 2025-06-18
**Current Status:** ✅ Working with privacy settings enabled
