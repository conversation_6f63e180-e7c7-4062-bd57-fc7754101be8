{"name": "mcq-tf-desktop", "version": "1.0.0", "description": "Desktop application for generating multiple-choice and true/false questions", "main": "src/main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "setup-gemini": "node install-gemini-cli.js", "test-gemini": "node test-gemini-integration.js", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "pack": "electron-builder --dir", "dist": "electron-builder --publish=never", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "dependencies": {"@google/generative-ai": "^0.24.1", "@pdf-lib/fontkit": "^1.1.1", "axios": "^1.6.2", "canvas": "^3.1.0", "chalk": "^4.1.2", "crypto": "^1.0.1", "crypto-js": "^4.2.0", "docx": "^9.5.1", "dotenv": "^16.4.7", "form-data": "^4.0.2", "mammoth": "^1.9.0", "moment-hijri": "^3.0.0", "node-fetch": "^3.3.2", "node-qpdf": "^1.0.3", "pdf-creator-node": "^2.3.5", "pdf-lib": "^1.17.1", "pdf-merger-js": "^5.1.2", "pdf-parse": "^1.1.1", "pdf-poppler": "^0.2.1", "pdf2pic": "^3.2.0", "pdfjs-dist": "^3.9.179", "puppeteer": "^24.10.2", "sharp": "^0.33.5", "sqlite3": "^5.1.6", "tesseract.js": "^5.0.3", "three": "^0.177.0", "uuid": "^11.1.0", "xlsx": "^0.18.5"}}