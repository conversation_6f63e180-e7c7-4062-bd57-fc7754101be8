/**
 * Utility functions for file handling
 */

/**
 * Generate a unique filename for an uploaded file
 * @param {string} originalName - The original file name or null
 * @param {string} prefix - Optional prefix for the filename
 * @returns {string} A unique filename
 */
function generateFileName(originalName = null, prefix = 'upload') {
  const timestamp = Date.now();
  
  if (originalName) {
    // Keep the extension from the original file
    const ext = originalName.split('.').pop();
    if (ext && ext.length <= 5) {
      return `${prefix}_${timestamp}.${ext}`;
    }
  }
  
  // Default to no extension if we couldn't determine one
  return `${prefix}_${timestamp}`;
}

module.exports = {
  generateFileName
}; 