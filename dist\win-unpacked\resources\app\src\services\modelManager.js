const fs = require('fs');
const path = require('path');
const config = require('../config');
const logger = require('../utils/logger');
const apiService = require('./apiService'); // Add this import

// Enhanced queue state - simplified for single model
let activeRequests = 0;
let userQueues = {}; // Track queues per user
let requestQueue = [];
let userRequestCounts = {}; // Track how many requests each user has in flight
let userLastRequestTime = {}; // Track when each user made their last request

// Get concurrent settings from environment with fallbacks
const MAX_CONCURRENT_REQUESTS = parseInt(process.env.MAX_CONCURRENT_REQUESTS || '12', 10);
const MAX_REQUESTS_PER_MODEL = parseInt(process.env.MAX_REQUESTS_PER_MODEL || '3', 10);
const MAX_REQUESTS_PER_USER = parseInt(process.env.MAX_REQUESTS_PER_USER || '4', 10);
const USER_COOLDOWN_MS = 2000; // 2 seconds cooldown between user requests

// Add extraction-specific optimizations
const EXTRACTION_TIMEOUT_MS = parseInt(process.env.EXTRACTION_TIMEOUT_MS || '1200000', 10); // 20 minutes for extraction
const EXTRACTION_PARALLEL_WORKERS = parseInt(process.env.EXTRACTION_PARALLEL_WORKERS || '6', 10); // Number of parallel extraction workers
const EXTRACTION_BATCH_SIZE = parseInt(process.env.EXTRACTION_BATCH_SIZE || '3', 10); // Process batch size for extraction tasks

// Initialize model configuration
function initModelConfig() {
  const models = config.models;
  
  if (models.length === 0) {
    logger.error('No models configured! Please set at least one model in the MODELS environment variable.');
    return;
  }
  
  const primaryModel = typeof models[0] === 'string' ? models[0] : models[0].name;
  
  logger.info(`Model manager initialized with model: ${primaryModel}. Max concurrent requests: ${MAX_CONCURRENT_REQUESTS}`);
  logger.info(`Total configured models: ${models.length}`);
}

// Get the configured model (always returns the primary model)
function getModel() {
  const models = config.models;
  
  if (!models || models.length === 0) {
    logger.error('No models configured! User must add models through the UI.');
    return null;
  }
  
  // Get an available model using the apiService's selection logic
  return apiService.selectModel();
}

// Get all available models
function getAllModels() {
  return config.models || [];
}

// Calculate which model to use based on balance and performance
function getOptimalModel(fileType = null) {
  const models = config.models;
  
  if (!models || models.length === 0) {
    return null; // No fallback - user must configure models
  }
  
  // Special handling for different file types
  if (fileType === 'image') {
    // For images, use any available model from user's configuration
    if (models && models.length > 0) {
      // Use the first available model that's not rate-limited
      for (const model of models) {
        if (!apiService.isModelRateLimited(model)) {
          return model;
        }
      }
      // If all models are rate-limited, use the first one anyway
      return models[0];
    }
  }
  
  // Use the apiService's intelligent model selection that respects rate limits
  return apiService.selectModel();
}

// Calculate request priority - optimized for better throughput
function calculateRequestPriority(request) {
    let priority = 1;
    
    // Time-waiting factor: Older requests get higher priority
    const waitingTime = Date.now() - request.timestamp;
    if (waitingTime > 30000) { // 30 seconds
        priority += 1;
    }
    if (waitingTime > 120000) { // 2 minutes
        priority += 2;
    }
    
    // File type priority
    if (request.metadata && request.metadata.fileType) {
        // Text files are fastest to process
        if (request.metadata.fileType === 'text') {
            priority += 2; // Higher priority for text
        }
        // Images and PDFs are slower but common
        else if (request.metadata.fileType === 'image') {
            priority += 0.5;
        }
    }
    
    // Content length priority - shorter content is faster to process
    if (request.content) {
        const contentLength = request.content.length;
        if (contentLength < 5000) {
            priority += 1; // Small content is faster to process
        }
        else if (contentLength > 50000) {
            priority -= 0.5; // Large content needs more resources
        }
    }
    
    // Add priority for extraction tasks that are in progress
    if (request.metadata && request.metadata.isExtractionTask) {
        priority += 1.5; // Extraction tasks get higher priority to complete faster
    }
    
    return priority;
}

// Execute a request using the enhanced queue system
async function executeRequest(requestFn, userId = null) {
  return new Promise((resolve, reject) => {
    // Update user's last request time
    if (userId) {
      userLastRequestTime[userId] = Date.now();
    }
    
    // If userId is provided, check if user already has too many requests
    if (userId && (userRequestCounts[userId] || 0) >= MAX_REQUESTS_PER_USER) {
      // Add to user-specific queue if they have too many requests already
      if (!userQueues[userId]) {
        userQueues[userId] = [];
      }
      
      userQueues[userId].push({ fn: requestFn, resolve, reject });
      logger.debug(`User ${userId} queued request (exceeded their limit). Queue size: ${userQueues[userId].length}`);
      return;
    }
    
    // Add to main queue with priority info
    const priority = calculateRequestPriority(requestFn);
    const request = { fn: requestFn, resolve, reject, userId, priority, timestamp: Date.now() };
    
    // Add to queue sorted by priority
    insertSorted(requestQueue, request);
    
    // Increment user request count
    if (userId) {
      userRequestCounts[userId] = (userRequestCounts[userId] || 0) + 1;
    }
    
    // Log queue status
    logger.debug(`New request added to queue. Current size: ${requestQueue.length}, active: ${activeRequests}/${MAX_CONCURRENT_REQUESTS}`);
    
    // Process queue if we have capacity
    tryProcessQueue();
  });
}

// Insert request sorted by priority
function insertSorted(queue, request) {
  const index = queue.findIndex(r => r.priority < request.priority);
  if (index === -1) {
    queue.push(request);
  } else {
    queue.splice(index, 0, request);
  }
}

// Try to process as many queued requests as possible
function tryProcessQueue() {
  // Process multiple requests in parallel up to capacity
  const availableSlots = MAX_CONCURRENT_REQUESTS - activeRequests;
  
  // Nothing to do if we're at capacity or have no requests
  if (availableSlots <= 0 || requestQueue.length === 0) {
    return;
  }
  
  // Process as many requests as we can up to available capacity
  // Increased batch size to reduce overhead
  const numToProcess = Math.min(availableSlots, requestQueue.length, 5); // Process up to 5 at once
  
  for (let i = 0; i < numToProcess; i++) {
    processNextInQueue();
  }
}

// Process the next request in the queue
async function processNextInQueue() {
  if (requestQueue.length === 0 || activeRequests >= MAX_CONCURRENT_REQUESTS) {
    return;
  }
  
  // Get the next request (highest priority)
  const request = requestQueue.shift();
  
  // Process this request
  processRequest(request);
}

// Helper function to process a single request
async function processRequest(request) {
  // This request can be processed now
  activeRequests++;
  
  try {
    const result = await request.fn();
    request.resolve(result);
  } catch (error) {
    request.reject(error);
  } finally {
    activeRequests--;
    
    // Decrement user request count and process their queue if any
    if (request.userId) {
      userRequestCounts[request.userId] = Math.max(0, (userRequestCounts[request.userId] || 1) - 1);
      
      // Process user-specific queue if they're now under their limit
      if (userQueues[request.userId] && userQueues[request.userId].length > 0 && 
          (userRequestCounts[request.userId] || 0) < MAX_REQUESTS_PER_USER) {
        const userRequest = userQueues[request.userId].shift();
        
        // Add back to main queue with high priority
        insertSorted(requestQueue, {
          ...userRequest,
          userId: request.userId,
          priority: 10, // High priority
          timestamp: Date.now()
        });
        
        // Increment user counter again
        userRequestCounts[request.userId] = (userRequestCounts[request.userId] || 0) + 1;
      }
    }
    
    // Try to process multiple requests in parallel
    // This is more efficient than a single recursive call
    setTimeout(() => tryProcessQueue(), 0);
  }
}

// Get status of the model manager
function getModelStatus() {
  const model = getModel();
  
  // Get user queue statistics
  const userQueueStats = Object.keys(userQueues).map(userId => ({
    userId,
    queueLength: userQueues[userId].length,
    activeRequests: userRequestCounts[userId] || 0
  }));
  
  return {
    model,
    queueLength: requestQueue.length,
    activeRequests,
    userQueueStats,
    maxConcurrentRequests: MAX_CONCURRENT_REQUESTS,
    maxRequestsPerModel: MAX_REQUESTS_PER_MODEL,
    maxRequestsPerUser: MAX_REQUESTS_PER_USER
  };
}

// Load model stats for reporting
function getModelStats() {
  try {
    const statsPath = path.resolve(__dirname, '../../data/model-stats.json');
    if (fs.existsSync(statsPath)) {
      const stats = JSON.parse(fs.readFileSync(statsPath, 'utf8'));
      return stats;
    }
    return null;
  } catch (error) {
    logger.error('Error loading model stats:', error);
    return null;
  }
}

// Clear model status and reset counters - useful for error recovery
function resetModelStatus() {
  activeRequests = 0;
  userRequestCounts = {};
  userQueues = {};
  requestQueue = [];
  
  logger.info('Model manager status has been reset');
  return { success: true };
}

// Initialize when the module is loaded
initModelConfig();

// Add memory optimization function
function optimizeMemoryUsage() {
    if (global.gc) {
        try {
            global.gc();
            console.log('Manual garbage collection executed');
        } catch (e) {
            console.log('Memory optimization attempt failed:', e.message);
        }
    }
}

// Process multiple requests in a batch for efficiency
async function processBatch(requests, batchSize = EXTRACTION_BATCH_SIZE) {
  if (!requests || requests.length === 0) return [];
  
  // Create a batch of promises
  const batch = requests.slice(0, batchSize);
  const promises = batch.map(req => processRequest(req));
  
  try {
    // Execute all promises in parallel and wait for them to complete
    return await Promise.all(promises);
  } catch (error) {
    logger.error(`Error processing batch: ${error.message}`);
    // If batch fails, process individually to avoid complete failure
    const results = [];
    for (const req of batch) {
      try {
        results.push(await processRequest(req));
      } catch (innerError) {
        logger.error(`Error in individual request: ${innerError.message}`);
        results.push(null);
      }
    }
    return results;
  }
}

// Optimize content before sending to model
function optimizeContent(content, type) {
  if (!content) return content;
  
  try {
    // Skip optimization for very small content
    if (content.length < 1000) return content;
    
    // Remove redundant whitespace while preserving paragraph structure
    content = content.replace(/\s+/g, ' ');
    
    // Preserve paragraph breaks
    content = content.replace(/\.\s+/g, '.\n\n');
    
    // For very long content, ensure we preserve the most important parts
    if (content.length > 100000) {
      // Keep the first 40% and last 60% of the content to ensure context
      const firstPart = Math.floor(content.length * 0.4);
      const lastPart = Math.floor(content.length * 0.6);
      content = content.slice(0, firstPart) + '\n...\n' + content.slice(-lastPart);
    }
    
    return content;
  } catch (error) {
    logger.error(`Error optimizing content: ${error.message}`);
    return content; // Return original content on error
  }
}

module.exports = {
  getModel,
  getAllModels,
  getOptimalModel,
  executeRequest,
  getModelStatus,
  getModelStats,
  resetModelStatus,
  optimizeContent
}; 