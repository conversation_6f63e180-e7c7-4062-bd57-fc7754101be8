/**
 * Date utility functions for consistent date formatting across the application
 */

class DateUtils {
    /**
     * Format date for display based on language
     * @param {Date|string} date - Date to format
     * @param {string} language - Language code ('ar' or 'en')
     * @param {boolean} includeTime - Whether to include time
     * @returns {string} Formatted date string
     */
    static formatDate(date, language = 'en', includeTime = false) {
        try {
            // Ensure we have a valid Date object
            const dateObj = date instanceof Date ? date : new Date(date);
            
            // Check if date is valid
            if (isNaN(dateObj.getTime())) {
                console.warn('Invalid date provided:', date);
                return language === 'ar' ? 'تاريخ غير صالح' : 'Invalid Date';
            }

            const options = {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            };

            if (includeTime) {
                options.hour = '2-digit';
                options.minute = '2-digit';
                options.hour12 = false;
            }

            if (language === 'ar') {
                // Arabic formatting
                return dateObj.toLocaleDateString('ar-SA', options);
            } else {
                // English formatting
                return dateObj.toLocaleDateString('en-US', options);
            }
        } catch (error) {
            console.error('Error formatting date:', error);
            return language === 'ar' ? 'تاريخ غير صالح' : 'Invalid Date';
        }
    }

    /**
     * Format date for quiz titles
     * @param {Date|string} date - Date to format
     * @param {string} language - Language code ('ar' or 'en')
     * @returns {string} Formatted date for titles
     */
    static formatDateForTitle(date, language = 'en') {
        try {
            const dateObj = date instanceof Date ? date : new Date(date);
            
            if (isNaN(dateObj.getTime())) {
                return language === 'ar' ? 'تاريخ غير صالح' : 'Invalid Date';
            }

            if (language === 'ar') {
                return dateObj.toLocaleDateString('ar-SA', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });
            } else {
                return dateObj.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric'
                });
            }
        } catch (error) {
            console.error('Error formatting date for title:', error);
            return language === 'ar' ? 'تاريخ غير صالح' : 'Invalid Date';
        }
    }

    /**
     * Get current date and time as ISO string
     * @returns {string} ISO date string
     */
    static getCurrentISODate() {
        return new Date().toISOString();
    }

    /**
     * Get current date formatted for display
     * @param {string} language - Language code ('ar' or 'en')
     * @param {boolean} includeTime - Whether to include time
     * @returns {string} Formatted current date
     */
    static getCurrentFormattedDate(language = 'en', includeTime = false) {
        return this.formatDate(new Date(), language, includeTime);
    }

    /**
     * Format relative time (e.g., "2 hours ago")
     * @param {Date|string} date - Date to format
     * @param {string} language - Language code ('ar' or 'en')
     * @returns {string} Relative time string
     */
    static formatRelativeTime(date, language = 'en') {
        try {
            const dateObj = date instanceof Date ? date : new Date(date);
            
            if (isNaN(dateObj.getTime())) {
                return language === 'ar' ? 'تاريخ غير صالح' : 'Invalid Date';
            }

            const now = new Date();
            const diffMs = now - dateObj;
            const diffMinutes = Math.floor(diffMs / (1000 * 60));
            const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
            const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

            if (language === 'ar') {
                if (diffMinutes < 1) return 'الآن';
                if (diffMinutes < 60) return `منذ ${diffMinutes} دقيقة`;
                if (diffHours < 24) return `منذ ${diffHours} ساعة`;
                if (diffDays < 7) return `منذ ${diffDays} يوم`;
                return this.formatDate(dateObj, language);
            } else {
                if (diffMinutes < 1) return 'Just now';
                if (diffMinutes < 60) return `${diffMinutes} minutes ago`;
                if (diffHours < 24) return `${diffHours} hours ago`;
                if (diffDays < 7) return `${diffDays} days ago`;
                return this.formatDate(dateObj, language);
            }
        } catch (error) {
            console.error('Error formatting relative time:', error);
            return language === 'ar' ? 'تاريخ غير صالح' : 'Invalid Date';
        }
    }

    /**
     * Parse date string safely
     * @param {string} dateString - Date string to parse
     * @returns {Date|null} Parsed date or null if invalid
     */
    static parseDate(dateString) {
        try {
            if (!dateString) return null;
            
            const date = new Date(dateString);
            return isNaN(date.getTime()) ? null : date;
        } catch (error) {
            console.error('Error parsing date:', error);
            return null;
        }
    }

    /**
     * Check if a date is valid
     * @param {Date|string} date - Date to validate
     * @returns {boolean} True if valid
     */
    static isValidDate(date) {
        try {
            const dateObj = date instanceof Date ? date : new Date(date);
            return !isNaN(dateObj.getTime());
        } catch (error) {
            return false;
        }
    }

    /**
     * Format date for file names (safe characters only)
     * @param {Date|string} date - Date to format
     * @returns {string} File-safe date string
     */
    static formatDateForFileName(date) {
        try {
            const dateObj = date instanceof Date ? date : new Date(date);
            
            if (isNaN(dateObj.getTime())) {
                return 'invalid-date';
            }

            return dateObj.toISOString().split('T')[0]; // YYYY-MM-DD format
        } catch (error) {
            console.error('Error formatting date for filename:', error);
            return 'invalid-date';
        }
    }

    /**
     * Get date range for filtering
     * @param {string} range - Range type ('today', 'week', 'month')
     * @returns {Object} Start and end dates
     */
    static getDateRange(range) {
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        
        switch (range) {
            case 'today':
                return {
                    start: today,
                    end: new Date(today.getTime() + 24 * 60 * 60 * 1000 - 1)
                };
            
            case 'week':
                const weekStart = new Date(today);
                weekStart.setDate(today.getDate() - today.getDay());
                return {
                    start: weekStart,
                    end: new Date(weekStart.getTime() + 7 * 24 * 60 * 60 * 1000 - 1)
                };
            
            case 'month':
                const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
                const monthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0, 23, 59, 59, 999);
                return {
                    start: monthStart,
                    end: monthEnd
                };
            
            default:
                return {
                    start: new Date(0), // Beginning of time
                    end: now
                };
        }
    }
}

module.exports = DateUtils;
